package com.magnamedia.service.payment;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPayment;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractPaymentRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Logger;


@Service
public class PaymentChangedToReceivedOrReplacedService {
    protected static final Logger logger = Logger.getLogger(PaymentChangedToReceivedOrReplacedService.class.getName());

    @Autowired
    private PaymentService paymentService;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ClientRefundService clientRefundService;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;

    @Transactional
    public Map<String, Object> apply(Payment p, Contract c) {
        logger.info("payment id: " + p.getId());

        Map<String, Object> map = unfitToWorkBusinessRule(p, c);


        return map;
    }

    @Transactional
    public Map<String, Object> unfitToWorkBusinessRule(Payment p, Contract c) {
        Map<String, Object> map = new HashMap<>();
        // check if not maid visa return.
        if (!p.getContract().isMaidVisa()) return map;

        if (!p.getRequiredForUnfitToWork()) {
            if (!p.getStatus().equals(PaymentStatus.RECEIVED) ||
                    !paymentRepository.existsNonReceivedSdrPaymentByContract(c.getId())) {
                return map;
            }
        }

        logger.info("payment id: " + p.getId());
        map.put("requiredForUnfitToWork", Boolean.FALSE);

        Map<String, Object> m = clientRefundService.getMaidVisaClientBalance(c);
        double balance = (double) m.get("balance");
        if (balance <= 0) {
            logger.info("Balance is <= 0, handling SDR installment logic for payment: " + p.getId());
            handleSdrInstallmentLogic(p, c, m);
            return map;
        }

        clientRefundService.refundSdrPayment(c, m);

        List<Payment> otherContractPayments = paymentRepository.findByContractAndRequiredForUnfitToWorkAndIdNotIn(c,
                true, Collections.singletonList(p.getId()));

        for (Payment payment : otherContractPayments) {
            logger.info( "RequiredForUnfitToWork value is [True] for payment: " + payment.getId() + " and we change to [False]");
            payment.setRequiredForUnfitToWork(false);
            paymentService.updatePaymentSilent(payment);
        }

        List<ContractPayment> requiredSDRPayments = contractPaymentRepository.findSDRPaymentsByContractAndRequiredForUnfitToWorkTrue(c);

        requiredSDRPayments.forEach(cp -> {
            cp.setRequiredForUnfitToWork(false);
            contractPaymentRepository.save(cp);
        });

        Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class)
                .createToDoIfValid(c, null, DirectDebitCancellationToDoReason.PAYMENT_RECEIVAL_NO_NEED_MONEY);

        return map;
    }

    @Transactional
    public void handleSdrInstallmentLogic(Payment p, Contract c, Map<String, Object> balanceMap) {

        logger.info("Handling SDR installment logic for payment: " + p.getId() + ", contract: " + c.getId());

        if (paymentRepository.existsNonReceivedSdrPaymentByContract(c.getId())) return;

        clientRefundService.sendMvFailedMedicalEMail(c, balanceMap);
    }
}