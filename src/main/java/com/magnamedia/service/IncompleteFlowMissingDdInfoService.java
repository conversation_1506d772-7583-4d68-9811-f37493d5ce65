package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig.FlowSubEventName;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.FlowEventConfigRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @created 14/07/2025 - 9:55 AM
 * ACC-9677
 */
@Service
public class IncompleteFlowMissingDdInfoService {
    private static final Logger logger = Logger.getLogger(IncompleteFlowMissingDdInfoService.class.getName());

    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;

    public void validateAndStartIncompleteFlowMissingDdInfo(DirectDebit dd) {

        if (!dd.getContractPaymentTerm().getContract().getStatus().equals(ContractStatus.ACTIVE) ||
                (dd.getCategory().equals(DirectDebitCategory.A) && !dd.getMStatus().equals(DirectDebitStatus.IN_COMPLETE)) ||
                (dd.getCategory().equals(DirectDebitCategory.B) && !dd.getStatus().equals(DirectDebitStatus.IN_COMPLETE)) ||
                dd.getDirectDebitRejectionToDo() != null || dd.getDirectDebitBouncingRejectionToDo() != null) {

            logger.info("Cannot run a new Incomplete Flow for dd Id: " + dd.getId() + " -> exiting");
            return;
        }

        FlowProcessorEntity flow = flowProcessorService.getFirstRunningFlow(
                        dd.getContractPaymentTerm().getContract(),
                        FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO);

        if (flow != null) {
            if (flow.getStatus() != null && flow.getStatus().equals(FlowProcessorEntity.FlowProcessorStatus.FROZEN)) {
                flow.setStatus(FlowProcessorEntity.FlowProcessorStatus.ACTIVE);
                flowProcessorEntityRepository.save(flow);
                logger.info("Re-run the incomplete flow after it was paused for flow Id: " + flow.getId() +
                        "; DD ID: " + dd.getId());
            } else {
                logger.info("An incomplete flow is currently running for the dd Id: " + dd.getId() + " -> exiting");
            }
            return;
        }

        logger.info("Start Incomplete Flow for ddc Id: " + dd.getContractPaymentTerm().getDdcId());
        startIncompleteFlowMissingDdInfo(dd.getContractPaymentTerm(), dd);
    }

    // Trigger “Incomplete flow / Data entry rejection”
    public void startIncompleteFlowMissingDdInfo(ContractPaymentTerm cpt, DirectDebit dd) {
        Map<String, Object> m = validateFlowStopping(cpt, null);
        if ((boolean) m.get("stopped") || (boolean) m.get("completed")) return;

        logger.info("Start Incomplete flow cpt id: " + cpt.getId());
        Map<String, Object> map = new HashMap<>();
        map.put("trials", 1);
        map.put("reminders", 0);
        map.put("lastExecutionDate", new Date());
        map.put("directDebit", dd);

        FlowSubEventConfig.FlowSubEventName flowSubEventName = IncompleteFlowMissingDdInfoService
                .getDDMessagingFlowStatus(
                        cpt.getContract(),
                        FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO);

        flowProcessorService.createFlowProcessor(
                FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO,
                flowSubEventName, cpt, map);
    }

    public void messagingFilter(
            SelectFilter selectFilter,
            FlowProcessorEntity entity){

        // 1- Filter by contractType and flowStatus
        String contractType = DDMessagingService.isContractMvSdr(entity.getContract()) ?
                DDMessagingContractType.MAID_VISA_SDR.getLabel() :
                entity.getContract().getContractProspectType().getCode();
        selectFilter.and("contractProspectTypes", "like", "%" + contractType + "%");

        // 2- Filter by RejectCategory
        ContractPaymentTerm cpt = entity.getContractPaymentTerm();
        DirectDebitDataEntryRejectCategory RejectCategory = null;

        logger.info("cpt IsAccountHolderRejected: " + cpt.getIsAccountHolderRejected() +
                "; IsIBANRejected: " + cpt.getIsIBANRejected() +
                "; IsEidRejected: " + cpt.getIsEidRejected());

        if (cpt.getIsAccountHolderRejected() && cpt.getIsIBANRejected() && cpt.getIsEidRejected()) {
            RejectCategory = DirectDebitDataEntryRejectCategory.AllDataIncorrect;
        } else if (cpt.getIsIBANRejected() && cpt.getIsEidRejected()) {
            RejectCategory = DirectDebitDataEntryRejectCategory.WrongEIDAndIBAN;
        } else if (cpt.getIsEidRejected() && cpt.getIsAccountHolderRejected()) {
            RejectCategory = DirectDebitDataEntryRejectCategory.WrongEIDAndAccountName;
        } else if (cpt.getIsIBANRejected() && cpt.getIsAccountHolderRejected()) {
            RejectCategory = DirectDebitDataEntryRejectCategory.WrongIBANAndAccountName;
        } else if (cpt.getIsAccountHolderRejected()) {
            RejectCategory = DirectDebitDataEntryRejectCategory.WrongAccountName;
        } else if (cpt.getIsIBANRejected()) {
            RejectCategory = DirectDebitDataEntryRejectCategory.WrongIBAN;
        } else if (cpt.getIsEidRejected()) {
            RejectCategory = DirectDebitDataEntryRejectCategory.WrongEID;
        }

        logger.info("Reject Category selected: " + RejectCategory);
        selectFilter.and("dataEntryRejectCategory", "=", RejectCategory);
    }

    public Map<String, Object> validateFlowStopping(ContractPaymentTerm cpt, FlowProcessorEntity f) {
        Map<String, Object> m = new HashMap<>();
        m.put("completed", false);
        m.put("stopped", false);

        if (!directDebitService.existsByDdStatuses(
                cpt,
                Arrays.asList(
                        DirectDebitStatus.IN_COMPLETE,
                        DirectDebitStatus.PENDING_DATA_ENTRY))) {
            logger.info("entity id: " + (f == null ? "NULL" : f.getId()) +
                    "; Flow stopping because there isn't any incomplete DDs");
            m.put("stopped", true);
            return m;
        }

        if (f.getStatus() != null &&
                !f.getStatus().equals(FlowProcessorEntity.FlowProcessorStatus.FROZEN) &&
                Setup.getApplicationContext()
                        .getBean(DirectDebitRejectionFlowService.class)
                        .doesClientHavePendingDesignerToDo(cpt.getContract())) {
            f.setStatus(FlowProcessorEntity.FlowProcessorStatus.FROZEN);
            flowProcessorEntityRepository.save(f);
        }

        return m;
    }

    // ACC-8951
    public static FlowSubEventName getDDMessagingFlowStatus(Contract contract, FlowEventConfig.FlowEventName flowEventName) {
        return getDDMessagingFlowStatus(contract, Setup.getRepository(FlowEventConfigRepository.class).findByName(flowEventName));
    }

    public static FlowSubEventName getDDMessagingFlowStatus(Contract contract, FlowEventConfig flowEventConfig) {
        int xDays = Utils.parseValue(flowEventConfig.getTagValue("incomplete_flow_before_x_days_paid_end_date_period").getValue(), Integer.class);

        boolean isNotPaid = !new LocalDate().isBefore(new LocalDate(contract.getPaidEndDate()).minusDays(xDays)) ||
                DDMessagingService.isContractMvSdr(contract);

        switch (flowEventConfig.getName()) {
            case INCOMPLETE_FLOW_MISSING_BANK_INFO:
                return isNotPaid ?
                        FlowSubEventName.MISSING_BANK_INFO_NOT_PAID :
                        FlowSubEventName.MISSING_BANK_INFO_PAID;
            case INCOMPLETE_FLOW_MISSING_DD_INFO:
                return isNotPaid ?
                        FlowSubEventName.MISSING_DD_INFO_NOT_PAID :
                        FlowSubEventName.MISSING_DD_INFO_PAID;
            default:
                return null;
        }
    }
}