package com.magnamedia.service.RiskDocumentsManagement;

import com.magnamedia.core.Setup;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementLayer;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementRole;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementTodo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementLayerRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementRoleRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementToDoRepository;
import com.magnamedia.service.MessagingService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class RiskDocumentsManagementService {

    private static final Logger logger = Logger.getLogger(RiskDocumentsManagementService.class.getName());

    @Autowired
    private RiskDocumentsManagementRepository riskDocumentsManagementRepository;
    @Autowired
    private RiskDocumentsManagementToDoService riskDocumentsManagementToDoService;
    @Autowired
    private RiskDocumentsManagementLayerRepository riskDocumentsManagementLayerRepository;

    public void closeDocument(RiskDocumentsManagement document, boolean activate) {
        document.setActive(activate);
        document.setStatus(activate ? RiskDocumentsManagement.Status.ACTIVE : RiskDocumentsManagement.Status.DISABLED);
        riskDocumentsManagementRepository.save(document);

        if (!activate) {
            riskDocumentsManagementToDoService.closeAllOpenedTodosByDocument(document);
        }
    }

    public void processDeletedRoleDocument(RiskDocumentsManagementRole role) {
        Page<RiskDocumentsManagement> p;
        Long lastId = -1L;
        do {
            p = riskDocumentsManagementRepository.getRiskDocumentsManagementByRoleIds(lastId, role,
                    PageRequest.of(0, 200));

            p.getContent()
                    .forEach(d -> {
                        List<RiskDocumentsManagementRole> documentRoles = d.getAccountablePerson();
                        if (documentRoles.size() == 1) {
                            d.setStatus(RiskDocumentsManagement.Status.EXPIRED);
                            d.setActive(false);
                            riskDocumentsManagementRepository.silentSave(d);
                            return;
                        }
                        documentRoles.remove(role);
                        d.setAccountablePerson(documentRoles);
                        riskDocumentsManagementRepository.silentSave(d);
                    });

            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() -1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void processDeletedRoleLayer(RiskDocumentsManagementRole role) {
        Page<RiskDocumentsManagementLayer> p;
        Long lastId = -1L;
        do {
            p = riskDocumentsManagementLayerRepository.getRiskDocumentsManagementLayerByRoleIds(lastId, role,
                    PageRequest.of(0, 200));

            p.getContent()
                    .forEach(l -> {
                        List<RiskDocumentsManagementRole> layerRoles = l.getRoleAssignee();
                        if (layerRoles.size() == 1) throw new BusinessException("There's a document that contains only this role, name: " + l.getRiskDocumentsManagement().getName());
                        layerRoles.remove(role);
                        l.setRoleAssignee(layerRoles);
                        riskDocumentsManagementLayerRepository.silentSave(l);
                    });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() -1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void sendRenewalEmail(RiskDocumentsManagement document) {
        List<RiskDocumentsManagementLayer> layers = document.getLayers();

        List<RiskDocumentsManagementRole> l = layers.stream()
                .flatMap(r -> r.getRoleAssignee().stream()).collect(Collectors.toList());

        Map<String, String> parameters = new HashMap<>();
        parameters.put("document_name", document.getName());
        parameters.put("new_expiry_date", new LocalDate(document.getExpiryDate()).toString("yyyy-MM-dd"));

        parameters.put("link_to_screen", Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) +
                "#!/" + Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_RISK_DOCUMENT_MGMT_PAGE));

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "risk_document_renewal_alert",
                        parameters,
                        RiskDocumentsManagementRoleService.getEmailsFromUsers(l),
                        document.getName() + " has been successfully Renewed!");
    }

    public void UpdateDocumentVisitDate(RiskDocumentsManagement updated, RiskDocumentsManagement origin) {
        if (origin.getNextVisitDate() == null ||
                origin.getNextVisitDate().equals(updated.getNextVisitDate())) return;

        if (origin.getNextVisitDate().before(new LocalDate().plusDays(1).toDate())) {

            List<RiskDocumentsManagementTodo> documentPendingVisitToDos = Setup.getRepository(RiskDocumentsManagementToDoRepository.class)
                    .findPendingVisitToDoByRiskDocumentsManagement(updated);

            logger.info("Update document next visit date: " + updated.getNextVisitDate() + " id: " + updated.getId());
            logger.info("risk document todos from size: " + documentPendingVisitToDos.size());

            if (documentPendingVisitToDos.isEmpty()) return;

            documentPendingVisitToDos.forEach(todo -> {
                todo.setStatus(RiskDocumentsManagementTodo.Status.CLOSED);
                Setup.getRepository(RiskDocumentsManagementToDoRepository.class).silentSave(todo);
            });
        }
    }

    public void UpdateDocumentExpiryDate(RiskDocumentsManagement updated, RiskDocumentsManagement origin) {

        if (updated.getExpiryDate() != null &&
                updated.getExpiryDate().equals(origin.getExpiryDate())) {
            return;
        }

        logger.info("Update document expiry date: " + updated.getExpiryDate() + " id: " + updated.getId());

        if (updated.getExpiryDate().after(new java.util.Date()) &&
                updated.getStatus().equals(RiskDocumentsManagement.Status.EXPIRED)) {

            updated.setStatus(RiskDocumentsManagement.Status.ACTIVE);

            List<RiskDocumentsManagementRole> documentRoles = Setup.getRepository(RiskDocumentsManagementRoleRepository.class)
                    .findDeletedRolesByRiskDocumentManagement(origin);

            logger.info("Document Role Size: " + documentRoles.size());

            documentRoles.forEach(role -> {
                role.setDeleted(false);
                Setup.getRepository(RiskDocumentsManagementRoleRepository.class).save(role);
            });
        }

        updateDocumentLayersDates(updated);
    }

    public void updateDocumentLayersDates(RiskDocumentsManagement updated) {

        List<RiskDocumentsManagementLayer> documentLayers = Setup.getRepository(RiskDocumentsManagementLayerRepository.class)
                .findByRiskDocumentsManagement(updated);

        logger.info("Document Layer Size: " + documentLayers.size());

        if (documentLayers.isEmpty()) return;

        documentLayers.forEach(l -> {
            if (l.getType().equals(RiskDocumentsManagementLayer.Type.LAYER_ONE)) {
                l.setCreationRenewalToDoDate(new Date(new LocalDate(updated.getExpiryDate())
                        .minusDays(l.getDaysBeforeExpiry()).toDate().getTime()));
            } else {
                l.setSendLayerTwoEmailDate(new Date(new LocalDate(updated.getExpiryDate())
                        .minusDays(l.getDaysBeforeExpiry()).toDate().getTime()));
            }
            Setup.getRepository(RiskDocumentsManagementLayerRepository.class).silentSave(l);
        });
    }
}
