package com.magnamedia.service.RiskDocumentsManagement;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementLayer;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementTodo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementToDoRepository;
import com.magnamedia.service.MessagingService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

@Service
public class RiskDocumentsManagementToDoService {

    private static final Logger logger = Logger.getLogger(RiskDocumentsManagementToDoService.class.getName());

    @Autowired
    private RiskDocumentsManagementRepository riskDocumentsManagementRepository;
    @Autowired
    private RiskDocumentsManagementToDoRepository riskManagementTodoRepository;

    public void createPendingRenewalToDoAndSendEmail(Long riskDocumentManagementId, RiskDocumentsManagementLayer l) {
        logger.info("layer id: " + l.getId());

        // The check for existing todos is now done in the findByCreationRenewalToDoDateAndType query

        RiskDocumentsManagementTodo todo = new RiskDocumentsManagementTodo();
        todo.setStatus(RiskDocumentsManagementTodo.Status.PENDING_RENEWAL);
        RiskDocumentsManagement document = riskDocumentsManagementRepository.findOne(riskDocumentManagementId);

        logger.info("document id: " + document.getId() +
                        "; type: " + document.getType() +
                        "; code: " + document.getType().getCode());

        todo.setType(document.getType().getCode().equals("vehicles") ?
                RiskDocumentsManagementTodo.Type.VEHICLE :
                RiskDocumentsManagementTodo.Type.NON_VEHICLE);

        document.setStatus(RiskDocumentsManagement.Status.UNDER_RENEWAL);
        todo.setRiskDocumentsManagement(document);
        todo.setRiskDocumentsManagementLayer(l);
        riskManagementTodoRepository.save(todo);

        riskDocumentsManagementRepository.save(document);

        sendLayerOneEmail(todo);
    }

    private void sendLayerOneEmail(RiskDocumentsManagementTodo todo) {
        logger.info("todo id: " + todo.getId());

        Map<String, String> parameters = new HashMap<>();
        parameters.put("document_name", todo.getRiskDocumentsManagement().getName());
        parameters.put("expiry_date", new LocalDate(todo.getRiskDocumentsManagement().getExpiryDate()).toString("yyyy-MM-dd"));
        parameters.put("layer_one_days_before_expiry", String.valueOf(todo.getRiskDocumentsManagementLayer()
                .getDaysBeforeExpiry()));

        parameters.put("on_it_action", Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) +
                "#!/" + Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_RISK_DOCUMENT_MGMT_PAGE) + "/on_it/" + todo.getId());

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "risk_document_layer_one_alert",
                        parameters,
                        RiskDocumentsManagementRoleService.getEmailsFromUsers(todo.getRiskDocumentsManagementLayer()
                                .getRoleAssignee()),
                        todo.getRiskDocumentsManagement().getName() + " is expiring in " +
                                todo.getRiskDocumentsManagementLayer().getDaysBeforeExpiry() + " days ");
    }

    public void createVisitTodoAndSendEmail(RiskDocumentsManagement document) {


        if (riskManagementTodoRepository
                .existsVisitToDosByDocumentAndCreationDateBetween(document.getId(),
                        new DateTime().withTimeAtStartOfDay().toDate(),
                        new DateTime().plusDays(1).withTimeAtStartOfDay().minusMillis(1).toDate())) return;

        RiskDocumentsManagementTodo visitTodo = new RiskDocumentsManagementTodo();
        visitTodo.setStatus(RiskDocumentsManagementTodo.Status.PENDING_VISIT);
        visitTodo.setType(RiskDocumentsManagementTodo.Type.VISIT);
        visitTodo.setRiskDocumentsManagement(document);
        visitTodo.setVisitDate(document.getLastVisitDate());
        visitTodo.setDueDateForNextVisit(document.getNextVisitDate());

        riskManagementTodoRepository.save(visitTodo);

        sendRequiresVisitEmail(document);
    }

    public void closeAllOpenedTodosByDocument(RiskDocumentsManagement document) {
        List<RiskDocumentsManagementTodo> documentToDos = riskManagementTodoRepository
                .findByRiskDocumentsManagement(document);

        if (documentToDos.isEmpty()) return;

        documentToDos
                .forEach(todo -> {
                    todo.setStatus(RiskDocumentsManagementTodo.Status.CLOSED);
                    riskManagementTodoRepository.silentSave(todo);
                });
    }

    private void sendRequiresVisitEmail(RiskDocumentsManagement document) {
        logger.info("document id: " + document.getId());

        Map<String, String> parameters = new HashMap<>();
        parameters.put("service_report_name", document.getServiceReportName());

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "requires_visit_alert",
                        parameters,
                        RiskDocumentsManagementRoleService.getEmailsFromUsers(document.getAccountablePerson()),
                        document.getServiceReportName() + " visit is scheduled for today!");
    }
}