/*
package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Transaction;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.repository.TransactionRepository;
import com.magnamedia.service.TransactionService;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

*/
/**
 * <AUTHOR> <<EMAIL>>
 * Created on 18/8/2020
 * Jirra ACC-2383
 *//*

@BusinessRule(moduleCode = "", entity = Attachment.class,
        events = {BusinessEvent.AfterCreate, BusinessEvent.BeforeUpdate},
        fields = { "id", "name", "path", "size", "tag", "extension", "amazon", "ownerId", "ownerType"})
public class AttachmentBusinessRule implements BusinessAction<Attachment> {

    private static final Logger logger = Logger.getLogger(AttachmentBusinessRule.class.getName());

    @Override
    public boolean validate(Attachment entity, BusinessEvent event) {

        logger.log(Level.INFO, "entity id: " + entity.getId() +
                "; tag: " + entity.getTag() +
                "; entity owner id: " + entity.getOwnerId() +
                "; entity owner type: " + entity.getOwnerType() +
                "; event: " + event);

        if (event == BusinessEvent.AfterCreate) {
            return (entity.getOwnerType() != null && entity.getOwnerType().equalsIgnoreCase("Payment"));
        }

        if (event == BusinessEvent.BeforeUpdate) {
            SelectQuery<Attachment> historyQuery = new SelectQuery<>(Attachment.class);
            historyQuery.filterBy("id", "=", entity.getId());
            historyQuery.sortBy("lastModificationDate", false, true);

            List<Attachment> oldAttachment = historyQuery.execute();

            if (oldAttachment != null && !oldAttachment.isEmpty()) {
                logger.log(Level.INFO, "oldAttachment: " + oldAttachment.size());
                logger.log(Level.INFO, "entity old owner id: " + oldAttachment.get(0).getOwnerId());
                logger.log(Level.INFO, "entity old owner type: " + oldAttachment.get(0).getOwnerType());

                return (oldAttachment.get(0).getOwnerType() == null && entity.getOwnerType() != null &&
                        entity.getOwnerType().equalsIgnoreCase("Payment"));
            }

            logger.log(Level.INFO, "oldAttachment: null or empty");

            Attachment one = Setup.getRepository(AttachementRepository.class).findOne(entity.getId());

            if (one != null) {
                logger.log(Level.INFO, "entity one owner id: " + one.getOwnerId());
                logger.log(Level.INFO, "entity one owner type: " + one.getOwnerType());
            } else {
                logger.log(Level.INFO, "one: null");
            }

            return ((one == null || one.getOwnerType() == null) &&
                    entity.getOwnerType() != null && entity.getOwnerType().equalsIgnoreCase("Payment"));
        }

        return false;
    }

    @Override
    public Map<String, Object> execute(Attachment entity, BusinessEvent even) {
        Setup.getApplicationContext().getBean(TransactionService.class)
                .addAttachmentFromPayment(entity);
        return null;
    }
}*/