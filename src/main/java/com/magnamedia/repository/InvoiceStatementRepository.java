package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.InvoiceStatement;
import com.magnamedia.entity.Supplier;
import com.magnamedia.extra.InvoiceStatementStatus;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InvoiceStatementRepository extends BaseRepository<InvoiceStatement> {

    List<InvoiceStatement> findByStatus(InvoiceStatementStatus status);
}
