package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;

@Repository
public interface HousemaidPayrollLogRepository extends BaseRepository<HousemaidPayrollLog> {
    
    @Query("SELECT DISTINCT h.payrollMonth " +
            "FROM HousemaidPayrollLog h " +
            "WHERE h.payrollAccountantTodo.id = :todoId")
    List<Date> findDistinctPayrollMonthsByTodoId(@Param("todoId") Long todoId);
} 