package com.magnamedia.repository.RiskDocumentsManagement;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface RiskDocumentsManagementHistoryRepository extends BaseRepository<RiskDocumentsManagementHistory> {

    @Query("select h from RiskDocumentsManagementHistory h " +
            "where h.riskDocumentsManagement.id = ?1")
    Page<RiskDocumentsManagementHistory> getRiskDocumentsManagementHistoriesByRiskDocumentsManagement(Long id, Pageable pageable);
}