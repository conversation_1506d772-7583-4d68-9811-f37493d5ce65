package com.magnamedia.repository.RiskDocumentsManagement;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface RiskDocumentsManagementRepository extends BaseRepository<RiskDocumentsManagement> {

    @Query("select r from RiskDocumentsManagement r where r.governmentEntity.code = ?1")
    Page<RiskDocumentsManagement> getRiskDocumentsManagementByGovernmentEntity(String governmentEntity, Pageable pageable);

    @Query("select d from RiskDocumentsManagement d where d.id > ?1 and " +
            "d.expiryDate < ?2 and d.status <> 'EXPIRED'")
    Page<RiskDocumentsManagement> getExpiredDocuments(Long id, java.sql.Date date, Pageable pageable);

    @Query("select r from RiskDocumentsManagement r " +
            "where r.id > ?1 and r.expiryDate = ?2 and r.requiresRenewal = true and r.status <> 'EXPIRED' and r.active = true")
    Page<RiskDocumentsManagement> getRequireRenewalsDocumentsByDate(Long id, java.sql.Date date, Pageable pageable);


    @Query("select r from RiskDocumentsManagement r " +
            "where r.id > ?1 and r.active = true and r.nextVisitDate = ?2 and r.requiresVisit = true and r.status <> 'EXPIRED'")
    Page<RiskDocumentsManagement> getRequireVisitsDocumentsByDate(Long id, java.sql.Date date, Pageable pageable);

    @Query("select d from RiskDocumentsManagement d  " +
            "where d.id > ?1 and ?2 member of d.accountablePerson")
    Page<RiskDocumentsManagement> getRiskDocumentsManagementByRoleIds(Long id, RiskDocumentsManagementRole role, Pageable pageable);
}