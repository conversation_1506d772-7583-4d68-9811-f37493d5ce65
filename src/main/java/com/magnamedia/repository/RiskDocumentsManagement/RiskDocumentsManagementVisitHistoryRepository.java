package com.magnamedia.repository.RiskDocumentsManagement;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementVisitHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface RiskDocumentsManagementVisitHistoryRepository extends BaseRepository<RiskDocumentsManagementVisitHistory> {

    @Query("select v from RiskDocumentsManagementVisitHistory v " +
            "where v.riskDocumentsManagement.id = ?1")
    Page<RiskDocumentsManagementVisitHistory> getRiskDocumentsManagementVisitHistoryByRiskDocumentsManagement(Long id, Pageable pageable);
}