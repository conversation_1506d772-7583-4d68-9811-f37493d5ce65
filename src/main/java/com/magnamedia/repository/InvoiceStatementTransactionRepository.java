package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.InvoiceStatement;
import com.magnamedia.entity.InvoiceStatementTransaction;
import com.magnamedia.extra.InvoiceStatementTransactionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface InvoiceStatementTransactionRepository extends BaseRepository<InvoiceStatementTransaction> {


    boolean existsByStatementAndHousemaid_IdAndType(
            InvoiceStatement statement, Long housemaid, InvoiceStatementTransactionType type);

    @Query("SELECT t FROM InvoiceStatementTransaction t " +
           "LEFT JOIN t.housemaid h " +
           "LEFT JOIN t.expenseRequestTodo e " +
           "WHERE (:statementId IS NULL OR t.statement.id = :statementId) " +
                "AND (:type IS NULL OR t.type = :type) AND t.type != 'DELETED' " +
                "AND t.confirmed <> true " +
                "AND (:maidName IS NULL OR :maidName = '' OR " +
                "     LOWER(h.name) LIKE LOWER(CONCAT('%', :maidName, '%')) OR " +
                "     LOWER(t.relatedMaidName) LIKE LOWER(CONCAT('%', :maidName, '%'))) " +
                "AND (e IS NULL OR e.status != 'CANCELED')" +
                "AND (:passportNumber IS NULL OR :passportNumber = '' OR LOWER(h.passportNumber) LIKE LOWER(CONCAT('%', :passportNumber, '%'))) " +
                "AND (:fromDate IS NULL OR (CASE WHEN e IS NOT NULL THEN e.creationDate ELSE t.visitDate END) >= :fromDate) " +
                "AND (:toDate IS NULL OR (CASE WHEN e IS NOT NULL THEN e.creationDate ELSE t.visitDate END) <= :toDate)")
    Page<InvoiceStatementTransaction> searchTransactions(
            @Param("statementId") Long statementId,
            @Param("type") InvoiceStatementTransactionType type,
            @Param("maidName") String maidName,
            @Param("passportNumber") String passportNumber,
            @Param("fromDate") Date fromDate,
            @Param("toDate") Date toDate,
            Pageable pageable);

    @Query("SELECT t FROM InvoiceStatementTransaction t " +
            "INNER JOIN t.statement s " +
            "LEFT JOIN t.housemaid h " +
            "LEFT JOIN t.expenseRequestTodo e " +
            "WHERE s.id = :statementId AND t.type = 'MISMATCHED' AND " +
                "(h IS NULL OR e IS NULL OR " +
                    "(e.status != 'CANCELED' AND e.lastModificationDate > s.creationDate))")
    List<InvoiceStatementTransaction> findTransactionsForRematch(
            @Param("statementId") Long statementId);

    @Query("SELECT COALESCE(SUM(t.amount), 0.0) FROM InvoiceStatementTransaction t " +
           "WHERE t.statement.id = :statementId " +
           "AND t.type = :type " +
           "AND t.type != 'DELETED'")
    Double sumAmountByStatementIdAndType(@Param("statementId") Long statementId,
                                        @Param("type") InvoiceStatementTransactionType type);

    @Query("SELECT COALESCE(SUM(t.amount), 0.0) FROM InvoiceStatementTransaction t " +
           "WHERE t.statement.id = :statementId " +
           "AND t.confirmed <> true " +
           "AND t.type IN :types " +
           "AND t.type != 'DELETED'")
    Double sumAmountByStatementIdAndTypes(@Param("statementId") Long statementId,
                                         @Param("types") List<InvoiceStatementTransactionType> types);

    @Query("SELECT DISTINCT t.expenseRequestTodo.id FROM InvoiceStatementTransaction t " +
           "WHERE t.statement.id = :statementId " +
                "AND t.type IN :types AND t.type != 'DELETED' " +
                "AND t.expenseRequestTodo IS NOT NULL " +
                "AND (t.confirmed IS NULL OR t.confirmed = false)")
    List<Long> findUsedExpenseRequestTodoIdsByStatementIdAndTypes(
            @Param("statementId") Long statementId,
            @Param("types") List<InvoiceStatementTransactionType> types);
}
