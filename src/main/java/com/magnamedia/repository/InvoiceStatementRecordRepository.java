package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.InvoiceStatement;
import com.magnamedia.entity.InvoiceStatementRecord;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InvoiceStatementRecordRepository extends BaseRepository<InvoiceStatementRecord> {

    List<InvoiceStatementRecord> findByStatementAndDeletedFalse(InvoiceStatement statement);

}
