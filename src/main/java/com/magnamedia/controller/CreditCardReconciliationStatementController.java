package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.CreditCardReconciliationStatementDetailsProjection;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.VatType;
import com.magnamedia.repository.*;
import com.magnamedia.service.CreditCardReconciliationStatementParsingService;
import com.magnamedia.service.ExpenseRequestService;
import com.magnamedia.service.ExpensePaymentService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2/4/2021
 */

@RestController
@RequestMapping("/CreditCardReconciliation")
public class CreditCardReconciliationStatementController extends BaseRepositoryController<CreditCardReconciliationStatement> {
    @Autowired
    CreditCardReconciliationStatementRepository repository;

    @Autowired
    CreditCardReconciliationStatementDetailsRepository detailsRepository;

    @Autowired
    CreditCardReconciliationStatementParsingService parsingService;

    @Autowired
    BucketRepository bucketRepository;
    @Autowired
    private ReconciliationTransactionRepository reconciliationTransactionRepository;
    @Autowired
    private TransactionRepository transactionRepository;
    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;
    @Autowired
    private ExpensePaymentRepository expensePaymentRepository;

    @Autowired
    private TransactionsController transactionsCtrl;
    @Autowired
    private ExpensePaymentService expensePaymentService;

    @Autowired
    private ExpenseRequestService expenseService;

    @Override
    public BaseRepository<CreditCardReconciliationStatement> getRepository() {
        return repository;
    }

    @Override
    protected ResponseEntity<?> deleteEntity(CreditCardReconciliationStatement entity) {

        List<CreditCardReconciliationStatementDetails> details = detailsRepository.findByCreditCardReconciliationStatementAndConfirmed(entity, true);
        if(details == null || details.isEmpty()){
            List<CreditCardReconciliationStatementDetails> allDetails =
                    detailsRepository.findByCreditCardReconciliationStatement_Id(entity.getId());
            detailsRepository.delete(allDetails);
            getRepository().delete(entity);
        }
        else{
            throw new RuntimeException("this statement can not be deleted because there are transactions related to it!");
        }
        return okResponse();
    }

    @PreAuthorize("hasPermission('CreditCardReconciliation','getByCreditCard')")
    @GetMapping("/getByCreditCard")
    public ResponseEntity<?> getByCreditCard(@RequestParam(required = false) Long creditId
            , Pageable pageable) {
        SelectQuery<CreditCardReconciliationStatement> query = new SelectQuery<>(CreditCardReconciliationStatement.class);
        query.filterBy("isDeleted", "=", Boolean.FALSE);
        if (creditId != null) {
            query.leftJoin("creditCard");
            query.filterBy("creditCard.id", "=", creditId);
        }
        query.sortBy("uploadDate", false);
        Page<CreditCardReconciliationStatement> result = query.execute(pageable);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('CreditCardReconciliation','deleteStatement')")
    @PostMapping("/deleteStatement/{id}")
    public ResponseEntity<?> deleteStatement(@PathVariable("id") CreditCardReconciliationStatement statement) {

        return deleteEntity(statement);
    }

    @PreAuthorize("hasPermission('CreditCardReconciliation','confirm-statement')")
    @PostMapping("/confirm-statement/{id}")
    public ResponseEntity<?> confirmStatement(@PathVariable("id") CreditCardReconciliationStatement statement) {
        if (statement == null) {
            throw new RuntimeException("record not found");
        }

        List<CreditCardReconciliationStatementDetails> creditCardReconciliationStatementDetails = detailsRepository.findByCreditCardReconciliationStatementAndConfirmed(statement, false);

        if (creditCardReconciliationStatementDetails == null || creditCardReconciliationStatementDetails.isEmpty()) {
            statement.setStatus(CreditCardReconciliationStatement.Status.CONFIRMED);
            super.updateEntity(statement);
        }

        return new ResponseEntity<>(HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('CreditCardReconciliation','uploadStatement')")
    @RequestMapping(value = "/uploadStatement/{cardId}",
            method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> uploadStatement(@PathVariable Long cardId,
                                             @RequestBody Long attachmentId) {
        Bucket bucket = bucketRepository.findOne(cardId);
        Attachment attachment = Storage.getAttchment(attachmentId);
        validateParsingNewRecord(bucket, attachment);

        CreditCardReconciliationStatement statement = new CreditCardReconciliationStatement();
        statement.setAuthorizedTransactions(0D);
        statement.setStatus(CreditCardReconciliationStatement.Status.PENDING);
        statement.setUploadDate(new Date());
        statement.setCreditCard(bucket);
        repository.save(statement);
        statement = parsingService.parseStatement(bucket, attachment, statement);

        bucket.setAuthorizedBalance(statement.getAuthorizedTransactions());
        return new ResponseEntity(statement, HttpStatus.OK);
    }

    private void validateParsingNewRecord(Bucket bucket, Attachment attachment) {
        if (bucket == null) {
            throw new RuntimeException("Credit Card not found");
        }

        List<CreditCardReconciliationStatement> creditCardReconciliationStatements = repository.findByCreditCardAndStatusAndIsDeleted(bucket, CreditCardReconciliationStatement.Status.PENDING, Boolean.FALSE);

        if (creditCardReconciliationStatements != null && !creditCardReconciliationStatements.isEmpty()) {
            throw new RuntimeException("Another pending statement for this Credit Card is still not confirmed, You need to confirm it to upload new statement");
        }

        if (attachment == null) {
            throw new RuntimeException("attachment not Found");
        }

        if (!attachment.getExtension().equals("xlsx") && !attachment.getExtension().equals("xls")) {
            throw new RuntimeException("attachment should be excel file");
        }
    }

    @PreAuthorize("hasPermission('CreditCardReconciliation','getDetails')")
    @GetMapping("/getDetails/{id}")
    public ResponseEntity<?> getDetails(@PathVariable Long id) {
        List<CreditCardReconciliationStatementDetails> details = detailsRepository.findByCreditCardReconciliationStatement_Id(id);
        return new ResponseEntity(project(details, CreditCardReconciliationStatementDetailsProjection.class), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('CreditCardReconciliation','reviewExpense')")
    @GetMapping("/reviewDetails/{id}")
    public ResponseEntity<?> reviewExpense(@PathVariable Long id) {
        CreditCardReconciliationStatementDetails one = detailsRepository.findOne(id);
        if (one == null) {
            throw new RuntimeException("id not found");
        }
        return new ResponseEntity(project(one, CreditCardReconciliationStatementDetailsProjection.class), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('CreditCardReconciliation','wrongMatch')")
    @PostMapping("/wrongMatch/{id}")
    public ResponseEntity<?> wrongMatch(@PathVariable Long id) {
        CreditCardReconciliationStatementDetails one = detailsRepository.findOne(id);
        if (one == null) {
            throw new RuntimeException("id not found");
        }
        one.setMatchType(CreditCardReconciliationStatementDetails.MatchType.UNMATCHED);
        one.setMatchedAutoDeduct(null);
        one.setReplenishmentTodo(null);
        one.setMatchedExpenseRequest(null);
        one.setReconciliationTransaction(null);
        detailsRepository.save(one);
        return okResponse();
    }

    @Transactional
    @PreAuthorize("hasPermission('CreditCardReconciliation','confirmExpense')")
    @PostMapping("/confirmExpense/{id}")
    public ResponseEntity<?> confirmExpense(
            @PathVariable Long id,
            @RequestBody(required = false) String description) {

        confirmExpenseRequest(id,
                CreditCardReconciliationStatementDetails.MatchType.EXISTING_EXPENSE,
                description);
        return okResponse();
    }

    @Transactional
    @PreAuthorize("hasPermission('CreditCardReconciliation','confirmAutoDeduct')")
    @PostMapping("/confirmAutoDeduct/{id}")
    public ResponseEntity<?> confirmAutoDeduct(@PathVariable Long id,
                                               @RequestBody(required = false) String description) {
        confirmExpenseRequest(id, CreditCardReconciliationStatementDetails.MatchType.AUTO_DEDUCTED, description);
        return okResponse();
    }

    private void confirmExpenseRequest(
            Long id,
            CreditCardReconciliationStatementDetails.MatchType matchType,
            String description) {

        CreditCardReconciliationStatementDetails one = detailsRepository.findOne(id);
        if (one == null) throw new RuntimeException("id not found");
        one.setConfirmed(true);
        detailsRepository.save(one);

        //creating transaction
        ReconciliationTransaction reconciliationTransaction = one.getReconciliationTransaction();
        ExpenseRequestTodo expenseRequestTodo = one.getMatchedExpenseRequest();
        Transaction transaction = createFromReconciliationTransaction(expenseRequestTodo.getRelatedToType(), expenseRequestTodo.getRelatedToId(),
                reconciliationTransaction, description, expenseRequestTodo.getExpensePayment());

        reconciliationTransactionRepository.save(reconciliationTransaction);

        expenseService.afterConfirmationTrigger(expenseRequestTodo, matchType, transaction);
    }

    @Transactional
    @PreAuthorize("hasPermission('CreditCardReconciliation','confirmRefund')")
    @PostMapping("/confirmRefund/{id}")
    public ResponseEntity<?> confirmRefund(
            @PathVariable Long id,
            @RequestBody(required = false) String description) {

        CreditCardReconciliationStatementDetails one = detailsRepository.findOne(id);
        if (one == null) throw new RuntimeException("id not found");
        one.setConfirmed(true);
        detailsRepository.save(one);

        ExpenseRequestTodo matchedExpenseRequest = one.getMatchedExpenseRequest();
        if (matchedExpenseRequest == null) {
            throw new RuntimeException("Matched Expense Request is not found");
        }

        if (matchedExpenseRequest.getConfirmed() == null || matchedExpenseRequest.getConfirmed() == false) {
            throw new RuntimeException("Related expense request is still unconfirmed. Please confirm it at first so you can confirm this refund.");
        }
        matchedExpenseRequest.setRefundConfirmed(true);
        expenseRequestTodoRepository.save(matchedExpenseRequest);

        ReconciliationTransaction reconciliationTransaction = one.getReconciliationTransaction();
        ExpenseRequestTodo expenseRequestTodo = one.getMatchedExpenseRequest();
        Transaction transaction = createFromReconciliationTransaction(expenseRequestTodo.getRelatedToType(), expenseRequestTodo.getRelatedToId(),
                reconciliationTransaction, description, expenseRequestTodo.getExpensePayment());

        reconciliationTransactionRepository.save(reconciliationTransaction);

        expenseService.afterConfirmationTrigger(expenseRequestTodo,
                CreditCardReconciliationStatementDetails.MatchType.REFUND,
                transaction);

        return okResponse();
    }

    @Transactional
    @PreAuthorize("hasPermission('confirmReplenishment','confirmRefund')")
    @PostMapping("/confirmReplenishment/{id}")
    public ResponseEntity<?> confirmReplenishment(
            @PathVariable Long id,
            @RequestBody(required = false) String description) {

        CreditCardReconciliationStatementDetails one = detailsRepository.findOne(id);
        if (one == null) throw new RuntimeException("id not found");
        one.setConfirmed(true);
        detailsRepository.save(one);

        BucketReplenishmentTodo replenishmentTodo = one.getReplenishmentTodo();
        replenishmentTodo.setConfirmed(true);
        replenishmentTodo.setTransactionAdded(true);
        Setup.getApplicationContext().getBean(BucketReplenishmentTodoRepository.class)
                .save(replenishmentTodo);

        ExpensePayment notCashPayment = null;
        if (replenishmentTodo.getExpensePayments() != null) {
            for (ExpensePayment expensePayment : replenishmentTodo.getExpensePayments()) {
                if (!expensePayment.getMethod().equals(ExpensePaymentMethod.CASH)) {
                    notCashPayment = expensePayment;
                }
            }
        }

        ReconciliationTransaction reconciliationTransaction = one.getReconciliationTransaction();
        Transaction transaction = createFromReconciliationTransaction(null, null, reconciliationTransaction, description, notCashPayment);
        if (notCashPayment != null){
            notCashPayment.setTransaction(transaction);
            notCashPayment.setConfirmed(true);
            expensePaymentRepository.save(notCashPayment);
        }

        reconciliationTransactionRepository.save(reconciliationTransaction);

        return okResponse();
    }

    @Transactional
    @PreAuthorize("hasPermission('CreditCardReconciliation','create-manual-transaction')")
    @PostMapping("/create-manual-transaction/{id}")
    public ResponseEntity<?> createManualTransaction(
            @PathVariable("id") CreditCardReconciliationStatementDetails details,
            @RequestBody Transaction transaction) {

        if (details == null) throw new RuntimeException("record not found");

        transaction = (Transaction) transactionsCtrl.createEntity(transaction).getBody();

        details.setConfirmed(true);
        details.setTransactionId(transaction.getId());
        detailsRepository.save(details);

        return okResponse();
    }



    @Transactional
    @PreAuthorize("hasPermission('matchWithExpense','confirmRefund')")
    @PostMapping("/matchWithExpense/{id}")
    public ResponseEntity<?> matchWithExpense(
            @PathVariable Long id,
            @RequestBody Long expenseId) {

        CreditCardReconciliationStatementDetails details = detailsRepository.findOne(id);
        if (details == null) throw new RuntimeException("id not found");

        ExpenseRequestTodo expenseTodo = Setup.getApplicationContext().getBean(ExpenseRequestTodoRepository.class).findOne(expenseId);
        ExpensePayment expensePayment = expenseTodo.getExpensePayment();
        if (expensePayment == null) throw new RuntimeException("Payment is null in expenseToDo: " + id);

        Bucket fromBucket = details.getCreditCardReconciliationStatement().getCreditCard();

        details.setMatchedExpenseRequest(expenseTodo);

        ReconciliationTransaction transaction = new ReconciliationTransaction();

        if (details.getCrdrAction() != null) switch(details.getCrdrAction()) {
            case CR:
                if(expenseTodo.getRefundConfirmed()) throw new RuntimeException("Refund is already confirmed");

                transaction.setFromBucket(fromBucket);
                transaction.setExpense(expenseTodo.getExpenseToPost() != null ?
                        expenseTodo.getExpenseToPost() : expenseTodo.getExpense());
                transaction.setAmount(details.getRecordAmount() * (-1));//in negative
                transaction.setTransactionDate(new Date());
                transaction.setPnlValueDate(new Date());
                transaction.setDescription(expenseTodo.getDescription());

                if (expensePayment.getVatAmount() != null && !expensePayment.getVatAmount().equals(0D)) {
                    transaction.setVatType(VatType.IN);
                    transaction.setVatAmount(expensePayment.getVatAmount() * (-1));//in negative
                    transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE,
                            AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
                } else {
                    transaction.setVatType(null);
                    transaction.setVatAmount(null);//in negative
                    transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE, "no_vat"));
                }

                for (Attachment attachment : expensePaymentService.getAttachments(expensePayment)) {
                    transaction.addAttachment(attachment);
                }

                transaction.setMissingTaxInvoice(parsingService.isMissingTaInvoice(transaction));
                transaction = reconciliationTransactionRepository.save(transaction);

                details.setRequestAmount(expenseTodo.getAmount());
                details.setRequestAmountInLocalCurrency(expenseTodo.getAmountInLocalCurrency());
                details.setRequestCurrency(expenseTodo.getCurrency());
                details.setReconciliationTransaction(transaction);
                details.setMatchType(CreditCardReconciliationStatementDetails.MatchType.REFUND);
                break;
            case DR:
                if(expenseTodo.getConfirmed()) throw new RuntimeException("Expense is already confirmed");

                transaction.setFromBucket(fromBucket);
                transaction.setExpense(
                        expenseTodo.getExpenseToPost() != null ?
                                expenseTodo.getExpenseToPost() :
                                expenseTodo.getExpense());

                if (expensePayment.getVatAmount() != null && !expensePayment.getVatAmount().equals(0D)) {
                    transaction.setVatType(VatType.IN);
                    transaction.setVatAmount(expensePayment.getVatAmount());
                    transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE,
                            AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
                } else {
                    transaction.setVatType(null);
                    transaction.setVatAmount(null);
                    transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE, "no_vat"));
                }

                for (Attachment attachment : expensePaymentService.getAttachments(expensePayment)) {
                    transaction.addAttachment(attachment);
                }

                transaction.setAmount(details.getRecordAmount());
                transaction.setTransactionDate(new Date());
                transaction.setPnlValueDate(new Date());
                transaction.setDescription(expenseTodo.getDescription());
                transaction.setPaymentType(PaymentMethod.valueOf(expenseTodo.getPaymentMethod().toString()));
                transaction.setMissingTaxInvoice(parsingService.isMissingTaInvoice(transaction));
                transaction = reconciliationTransactionRepository.save(transaction);

                details.setRequestAmount(expenseTodo.getAmount());
                details.setRequestAmountInLocalCurrency(expenseTodo.getAmountInLocalCurrency());
                details.setRequestCurrency(expenseTodo.getCurrency());
                details.setReconciliationTransaction(transaction);
                details.setMatchType(CreditCardReconciliationStatementDetails.MatchType.EXISTING_EXPENSE);
                break;
            default:
                throw new RuntimeException("Wrong action");
        }
        details = detailsRepository.save(details);

        return new ResponseEntity(project(details, CreditCardReconciliationStatementDetailsProjection.class), HttpStatus.OK);
    }

    private Transaction createFromReconciliationTransaction(
            ExpenseRelatedTo.ExpenseRelatedToType relatedToType, Long relatedToId,
            ReconciliationTransaction rTransaction,
            String description,
            ExpensePayment expensePayment) {

        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(new Date().getTime()));
        transaction.setPnlValueDate(new Date());
        transaction.setAmount(rTransaction.getAmount());
        transaction.setFromBucket(rTransaction.getFromBucket());
        transaction.setToBucket(rTransaction.getToBucket());
        transaction.setExpense(rTransaction.getExpense());
        transaction.setLicense(rTransaction.getLicense());
        transaction.setAmount(rTransaction.getAmount());
        transaction.setVatType(rTransaction.getVatType());
        transaction.setVatAmount(rTransaction.getVatAmount());
        transaction.setDescription(description);
        transaction.setPaymentType(rTransaction.getPaymentType());
        transaction.setMissingTaxInvoice(rTransaction.getMissingTaxInvoice());

        if (expensePayment != null) {
            transaction.setExpensePaymentId(expensePayment.getId());

            if(rTransaction.getFromBucket() != null &&
                    expensePayment.getFromBucket() != null &&
                    !rTransaction.getFromBucket().equals(expensePayment.getFromBucket())) {

                expensePayment.setFromBucket(rTransaction.getFromBucket());
                expensePaymentRepository.save(expensePayment);
            }
        }


        for (Attachment attachment : rTransaction.getAttachments()) {
            Attachment cloned = Storage.cloneTemporary(attachment, attachment.getTag());
            transaction.addAttachment(cloned);
        }

        parsingService.initTransactionRelatedToData(transaction, relatedToType, relatedToId);
        transaction = (Transaction) Setup.getApplicationContext().getBean(TransactionsController.class).createEntity(transaction).getBody();

        rTransaction.setTransaction(transaction);

        return transaction;
    }

    // Jira ACC-4505
    private ReconciliationTransaction createFromTransaction(Transaction transaction){
        ReconciliationTransaction reconciliationTransaction = new ReconciliationTransaction();
        reconciliationTransaction.setPnlValueDate(transaction.getPnlValueDate());
        reconciliationTransaction.setAmount(transaction.getAmount());
        reconciliationTransaction.setFromBucket(transaction.getFromBucket());
        reconciliationTransaction.setToBucket(transaction.getToBucket());
        reconciliationTransaction.setExpense(transaction.getExpense());
        reconciliationTransaction.setLicense(transaction.getLicense());
        reconciliationTransaction.setAmount(transaction.getAmount());
        reconciliationTransaction.setVatType(transaction.getVatType());
        reconciliationTransaction.setVatAmount(transaction.getVatAmount());
        reconciliationTransaction.setPaymentType(transaction.getPaymentType());
        reconciliationTransaction.setMissingTaxInvoice(transaction.getMissingTaxInvoice());
        reconciliationTransaction.setTransaction(transaction);
        return reconciliationTransaction;
    }

    // JIRA ACC-4505
    @GetMapping("/details/{details_id}/alreadyMatchedTransactions")
    public ResponseEntity<?> getAlreadyMatchedTransactions(
            @PathVariable("details_id") CreditCardReconciliationStatementDetails details,
            @RequestParam(name = "search", required = false) String search) {

        int thresholdDaysBefore = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ALREADY_MATCHED_TRANSACTIONS_BEFORE_X_DAYS_THRESHOLD));

        List<Transaction> l = transactionRepository.findAlreadyMatchedByBucketAndDateAndNotLinkedToDetails(
                details.getCreditCardReconciliationStatement().getCreditCard(),
                new LocalDate().minusDays(thresholdDaysBefore).toDate(),
                search);

        return ResponseEntity.ok(l);
    }

    @Transactional
    @PostMapping("/details/{details_id}/confirmAlreadyMatched")
    public ResponseEntity<?> confirmAlreadyMatched(
            @PathVariable("details_id") CreditCardReconciliationStatementDetails details,
            @RequestParam(name = "transactionId") Long transactionId) {

        Transaction transaction = transactionRepository.findOne(transactionId);
        if(transaction == null) throw new RuntimeException("Transaction cannot be found");

        ReconciliationTransaction reconciliationTransaction =
                reconciliationTransactionRepository.save(createFromTransaction(transaction));

        details.setTransactionId(transactionId);
        details.setConfirmed(Boolean.TRUE);
        details.setMatchType(CreditCardReconciliationStatementDetails.MatchType.ALREADY_MATCHED_TRANSACTION);
        details.setReconciliationTransaction(reconciliationTransaction);
        details.setPaymentDate(transaction.getDate());

        detailsRepository.save(details);

        return ResponseEntity.ok("Deduction confirmed!");
    }
}