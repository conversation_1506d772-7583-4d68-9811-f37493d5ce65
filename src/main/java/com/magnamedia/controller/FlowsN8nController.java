package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.*;

@RestController
@RequestMapping("/flowProcessorN8n")
public class FlowsN8nController extends BaseRepositoryController<FlowProcessorEntity> {

    @Autowired
    private FlowProcessorEntityRepository repository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private FlowProcessorService service;
    @Autowired
    private UnpaidOnlineCreditCardPaymentService onlineCardPaymentService;
    @Autowired
    private AfterCashFlowService afterCashFlowService;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private MaidVisaFailedMedicalCheckService maidVisaFailedMedicalCheckService;
    @Autowired
    private ContractService contractService;
    @Autowired
    private InterModuleConnector interModuleConnector;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private PaymentExpiryService paymentExpiryService;


    @Override
    public BaseRepositoryParent<FlowProcessorEntity> getRepository() {
        return repository;
    }

    @GetMapping("/activeFlows/{flow}") // don't cache this as records keep updating
    public ResponseEntity<?> activeFlows(
            @PathVariable(name = "flow") String flow) {

        return ResponseEntity.ok(repository
                .findIdByFlowEventConfig_NameAndStoppedFalseAndCompletedFalse(
                        FlowEventConfig.FlowEventName.valueOf(flow)));
    }

    @GetMapping("/validateFlowStopping/{id}")
    @Transactional
    public ResponseEntity<?> validateFlowStopping(
            @PathVariable(name = "id") FlowProcessorEntity flow) {

        if (!flow.getFlowEventConfig().hasTag("defaultDDSendTime"))
            throw new RuntimeException(flow.getFlowEventConfig().getName().getMessagingType().getLabel() +
                    " Flow does not have a default DD send time tag");

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("id", flow.getId());
            put("stopped", service.validateFlowStopping(flow));
        }});
    }

    @GetMapping("/checkNextStep/{id}")
    @Transactional
    public ResponseEntity<?> checkNextStep(
            @PathVariable(name = "id") FlowProcessorEntity flow) {

        return ResponseEntity.ok(service.checkNextStep(flow));
    }

    @PostMapping("/resolveDDMessaging/{id}")
    @Transactional
    public ResponseEntity<?> resolveDDMessaging(
            @PathVariable(name = "id") FlowProcessorEntity flow,
            @RequestBody Map<String, Object> body) {

        service.resolveDDMessaging(flow, body);

        return okResponse();
    }

    @PostMapping("/applyAfterProcessFlowSubEventConfig/{id}")
    @Transactional
    public ResponseEntity<?> applyAfterProcessFlowSubEventConfig(
            @PathVariable(name = "id") FlowProcessorEntity flow,
            @RequestBody Map<String, Object> body) {

        service.applyAfterProcessFlowSubEventConfig(flow, body);

        return okResponse();
    }

    @GetMapping("/validateOnlineCardReminderFlow/{id}")
    @Transactional
    public ResponseEntity<?> validateOnlineCardReminderFlow(
            @PathVariable(name = "id") FlowProcessorEntity flow) throws ParseException {

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("id", flow.getId());
            put("stopped", onlineCardPaymentService.shouldDelayTerminationMessageBeforePaidEndDate(flow));
        }});
    }

    @GetMapping("/validateIpamFlow/{id}")
    @Transactional
    public ResponseEntity<?> validateIpamFlow(
            @PathVariable(name = "id") FlowProcessorEntity flow) {

        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

        Tag xPaidPaymentsTagMV = flowEventConfig.getTagValue("mv_ipam_x_paid_payments_to_convert_paying_cc");
        Tag xPaidPaymentsTagCC = flowEventConfig.getTagValue("cc_ipam_x_paid_payments_to_convert_paying_cc");
        int xPaidPaymentsMV = xPaidPaymentsTagMV != null ? Integer.parseInt(xPaidPaymentsTagMV.getValue()) : 5;
        int xPaidPaymentsCC = xPaidPaymentsTagCC != null ? Integer.parseInt(xPaidPaymentsTagCC.getValue()) : 2;

        boolean stopped = afterCashFlowService.checkAndSwitchToPayingViaCC(
                flow,
                flow.getContract().isMaidCc()
                        ? xPaidPaymentsCC
                        : xPaidPaymentsMV);

        if (!stopped && flow.getContract().isPayingViaCreditCard()) {
            flow.setCompleted(true);
            repository.save(flow);

            stopped = true;
        }

        if (!stopped) {
            stopped = service.validateFlowStopping(flow);
        }

        final boolean s = stopped;

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("id", flow.getId());
            put("stopped", s);
        }});
    }

    @GetMapping("/fetchCptsForIPAMs")
    @Transactional
    public ResponseEntity<?> fetchCptsForIPAMs() {

        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

        return ResponseEntity.ok(new SelectQuery<>(
                afterCashFlowService.getCandidateCPTs(flowEventConfig.getId()), "",
                Long.class, new HashMap()).execute());
    }

    @GetMapping("/validatedIpamCreation/{id}")
    @Transactional
    public ResponseEntity<?> validatedIpamCreation(
            @PathVariable(name = "id") ContractPaymentTerm cpt) {

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("id", cpt.getId());
            put("validated", afterCashFlowService.validateFlowCreation(cpt));
        }});
    }

    @GetMapping("/createIpamFlow/{id}")
    @Transactional
    public ResponseEntity<?> createIpamFlow(
            @PathVariable(name = "id") ContractPaymentTerm cpt) {

        Map<String, Object> map = new HashMap<>();
        map.put("trials", 0);
        map.put("reminders", 1);
        map.put("lastExecutionDate", new DateTime(cpt.getContract().getStartOfContract())
                .dayOfMonth().withMinimumValue()
                .withHourOfDay(9).withMinuteOfHour(0).withSecondOfMinute(0)
                .toDate());

        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);
        if (flowEventConfig == null)
            throw new BusinessException("IPAM event config is not configured");


        FlowSubEventConfig noSignatureSubEvent = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE, flowEventConfig);
        if (noSignatureSubEvent == null)
            throw new BusinessException("IPAM subevent config is not configured");

        FlowProcessorEntity f = service.createFlowProcessor(
                flowEventConfig, noSignatureSubEvent,
                cpt, map);

        return ResponseEntity.ok(new HashMap<String, Long>() {{
            put("id", f.getId());
        }});
    }

    @GetMapping("/addAdditionalInfoForIpamFlow/{id}")
    @Transactional
    public ResponseEntity<?> addAdditionalInfoForIpamFlow(
            @PathVariable(name = "id") ContractPaymentTerm cpt) {

        contractService.addAdditionalInfoForIpamFlow(cpt.getContract());

        return okResponse();
    }

    @GetMapping("/cancelAllMonthlyDds/{id}")
    @Transactional
    public ResponseEntity<?> cancelAllMonthlyDds(
            @PathVariable(name = "id") ContractPaymentTerm cpt) {

        Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                .cancelAllMonthlyDdsByCpt(cpt, DirectDebitCancellationToDoReason.CLIENT_PAID_CASH_EXTENDING_AFTER_CASH_FLOW);

        return okResponse();
    }

    @GetMapping("/disableIpamThankYouMessage/{id}")
    @Transactional
    public ResponseEntity<?> disableIpamThankYouMessage(
            @PathVariable(name = "id") FlowProcessorEntity flow) {

        FlowSubEventConfig noSignatureSubEvent = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE, flow.getFlowEventConfig());

        if (flow.getTrials() < noSignatureSubEvent.getMaxTrials() - 1 &&
                flow.getCurrentFlowRun() > 1 && new DateTime().getDayOfMonth() == 1) {

            afterCashFlowService.disableThankYouMessage(flow.getContract());
        }

        return okResponse();
    }

    @GetMapping("/eventualResetIpamFlow/{id}")
    @Transactional
    public ResponseEntity<?> eventualResetIpamFlow(
            @PathVariable(name = "id") FlowProcessorEntity flow) {

        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

        FlowSubEventConfig noSignatureSubEvent = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE, flow.getFlowEventConfig());

        if (new DateTime().isAfter(new DateTime(flow.getContract().getPaidEndDate()))) {
            flow.setTrials(flow.getCurrentSubEvent().getMaxTrials() - 1);
            flow.setReminders(flow.getCurrentSubEvent().getMaxReminders());

            repository.save(flow);
        } else if (flow.getTrials() == 1) {
            int xDays = Integer.parseInt(
                    flowEventConfig.getTagValue(flow.getContract().isMaidCc() ?
                            "ipam_x_days_before_paid_end_date" : "ipam_x_days_before_adjusted_end_date").getValue());

            LocalDate d = new LocalDate(flow.getContract().isMaidCc()
                    ? flow.getContract().getPaidEndDate()
                    : flow.getContract().getAdjustedEndDate());

            if (new LocalDate().isBefore(d.minusDays(xDays))) {
                return ResponseEntity.ok(new HashMap<String, Object>() {{
                    put("id", flow.getId());
                    put("exit", true);
                    put("flowReset", false);
                }});
            }

            // ACC-8796 retain the salary with us (IPAM flow)
            if (flow.getContract().isMaidVisa() &&
                    maidVisaFailedMedicalCheckService.checkFailedMedicalsForPayingViaCc(flow)) {

                logger.info("Maid failed medical step -> skip");
                afterCashFlowService.resetFlow(flow.getId());

                return ResponseEntity.ok(new HashMap<String, Object>() {{
                    put("id", flow.getId());
                    put("exit", false);
                    put("flowReset", true);
                }});
            }

            flow.setLastExecutionDate(new DateTime()
                    .withHourOfDay(9).withMinuteOfHour(0).withSecondOfMinute(0)
                    .toDate());
            repository.save(flow);
        }

        if (flow.getTrials() == noSignatureSubEvent.getMaxTrials() - 1 &&
                flow.getCurrentFlowRun() < flowEventConfig.getMaxFlowRuns() &&
                service.nextMonthPaymentReceived(flow.getContract())) {

            afterCashFlowService.resetFlow(flow.getId());

            return ResponseEntity.ok(new HashMap<String, Object>() {{
                put("id", flow.getId());
                put("exit", false);
                put("flowReset", true);
            }});
        }

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("id", flow.getId());
            put("exit", false);
            put("flowReset", false);
        }});
    }

    @GetMapping("/validatePayingViaCcFlow/{id}")
    @Transactional
    public ResponseEntity<?> validatePayingViaCcFlow(
            @PathVariable(name = "id") FlowProcessorEntity flow) {

        boolean skipped = !flow.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.DD_SIGNING_OFFER) &&
                !flow.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.DD_Rejection) &&
                clientPayingViaCreditCardService.validateSkippingFlow(flow);

        boolean stopped = false;

        if(!skipped && flow.getCurrentSubEvent().getName()
                .equals(FlowSubEventConfig.FlowSubEventName.DD_SIGNING_OFFER) &&
                !flow.getContract().isPayingViaCreditCard()) {

            flow.setStopped(true);
            repository.save(flow);

            stopped = true;
        }

        if(!skipped && !stopped) {
            if (FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER.equals(flow.getCurrentSubEvent().getName()) &&
                    flow.getContract().getScheduledDateOfTermination() != null &&
                    ContractStatus.ACTIVE.equals(flow.getContract().getStatus())) {

                skipped = true;
            }
        }

        final boolean st = stopped;
        final boolean sk = skipped;

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("id", flow.getId());
            put("stopped", st);
            put("skipped", sk);
        }});
    }

    @GetMapping("/fetchCptsForPayingViaCcReminders")
    @Transactional
    public ResponseEntity<?> fetchCptsForPayingViaCcReminders() {

        int startMonthlyReminderDay = 8;

        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        if (flowEventConfig.hasTag("monthly_reminder_paying_cc_start_before_x_days")) {
            startMonthlyReminderDay = Integer.parseInt(
                    flowEventConfig.getTagValue("monthly_reminder_paying_cc_start_before_x_days").getValue());
        }

        Date d = new LocalDate().plusDays(startMonthlyReminderDay).toDate();

        return ResponseEntity.ok(Setup.getRepository(ContractPaymentTermRepository.class)
                .findIdByCptAndFlowProcessEntityAndPayingViaCreditCard(d));
    }

    @GetMapping("/createPayingViaCcReminderFlow/{id}")
    @Transactional
    public ResponseEntity<?> createPayingViaCcReminderFlow(
            @PathVariable(name = "id") ContractPaymentTerm cpt) {

        int startMonthlyReminderDay = 8;

        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        if (flowEventConfig.hasTag("monthly_reminder_paying_cc_start_before_x_days")) {
            startMonthlyReminderDay = Integer.parseInt(
                    flowEventConfig.getTagValue("monthly_reminder_paying_cc_start_before_x_days").getValue());
        }

        Date d = new LocalDate().plusDays(startMonthlyReminderDay).toDate();

        clientPayingViaCreditCardService.startReminderFlow(cpt,
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                startMonthlyReminderDay);

        return okResponse();
    }

    @GetMapping("/removeRecurringFlagAndCreateToDos")
    @Transactional
    public ResponseEntity<?> removeRecurringFlagAndCreateToDos() {

        clientPayingViaCreditCardService.removeRecurringFlagAndCreateToDos();

        return okResponse();
    }

    @GetMapping("/removePaymentsWhenErpParameterFalse")
    @Transactional
    public ResponseEntity<?> removePaymentsWhenErpParameterFalse() {

        clientPayingViaCreditCardService.removePaymentsWhenErpParameterFalse();

        return okResponse();
    }

    @GetMapping("/removePaymentsUponProviderChange")
    @Transactional
    public ResponseEntity<?> removePaymentsUponProviderChange() {

        clientPayingViaCreditCardService.removePaymentsUponProviderChange                       ();

        return okResponse();
    }

    @GetMapping("/removeTokenFromInactiveCpt")
    @Transactional
    public ResponseEntity<?> removeTokenFromInactiveCpt() {

        clientPayingViaCreditCardService.removeTokenFromInactiveCpt                       ();

        return okResponse();
    }

    @GetMapping("/startAutomaticCollectionRecurringCreditCardPayment")
    @Transactional
    public ResponseEntity<?> startAutomaticCollectionRecurringCreditCardPayment() {

        clientPayingViaCreditCardService.startAutomaticCollectionRecurringCreditCardPayment                       ();

        return okResponse();
    }

    @GetMapping("/handleFailedErpCaptureRecurringPayment")
    @Transactional
    public ResponseEntity<?> handleFailedErpCaptureRecurringPayment() {

        clientPayingViaCreditCardService.handleFailedErpCaptureRecurringPayment                       ();

        return okResponse();
    }

    @GetMapping("/activeFlows/incompleteDDs/{id}")
    @Transactional
    public ResponseEntity<?> activeFlowsIncompleteDDs(
            @PathVariable(name = "id") Long lastId) {

        return ResponseEntity.ok(repository.findRunningIncompleteFlowIdsMissingBankInfo(lastId,
                FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO,
                PageRequest.of(0, 200)));
    }

    @GetMapping("/activeFlows/expiryFlow/{id}")
    @Transactional
    public ResponseEntity<?> activeFlowsExpiryFlow(
            @PathVariable(name = "id") Long lastId) {

        int xDays = Integer.parseInt(Setup.getParameter(
                Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYMENT_EXPIRY_FLOW_BEFORE_X_DAYS_PAID_END_DATE_PERIOD));

        return ResponseEntity.ok(contractRepository.findIdsForPaymentExpiryFlow(
                lastId,
                new LocalDate().toDate(), new LocalDate().plusDays(xDays).toDate(),
                Arrays.asList(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW,
                        FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED,
                        FlowEventConfig.FlowEventName.EXTENSION_FLOW),
                PageRequest.of(0, 200)));
    }

    @GetMapping("/validateExpiryFlow/{id}")
    @Transactional
    public ResponseEntity<?> validateExpiryFlow(
            @PathVariable(name = "id") Contract contract) {

        Map<String, Object> skipped = new HashMap<String, Object>(){{
            put("id", contract.getId());
        }};

        if (contractService.hasPreventCreateOtherDds(contract)) {
            logger.info("contract has Prevent Create Other Dds -> exiting");

            skipped.put("skipped", true);
            return ResponseEntity.ok(skipped);
        }

        List<DirectDebitStatus> notAllowedStatuses = Arrays.asList(
                DirectDebitStatus.CANCELED, DirectDebitStatus.REJECTED,
                DirectDebitStatus.PENDING_FOR_CANCELLATION, DirectDebitStatus.EXPIRED);

        if (directDebitRepository.existsMonthlyDdCoverAfterPaidEndDate(contract,
                new LocalDate(contract.getPaidEndDate())
                        .plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                notAllowedStatuses)) {

            logger.info("contract has active monthly dd -> exiting");

            skipped.put("skipped", true);
            return ResponseEntity.ok(skipped);
        }

        if (directDebitRepository.existsRejectionFlowOfMonthlyDdCoverAfterPaidEndDate(contract,
                new LocalDate(contract.getPaidEndDate())
                        .plusMonths(1).dayOfMonth().withMinimumValue().toDate())) {

            logger.info("contract has rejection flow -> exiting");

            skipped.put("skipped", true);
            return ResponseEntity.ok(skipped);
        }

        if (new LocalDate(contract.getStartOfContract()).toString("yyyy-MM-dd")
                .equals(new LocalDate(contract.getPaidEndDate()).toString("yyyy-MM-dd")) ||
                directDebitService.contractHasOpenMainDdcToDo(contract.getId()) ) {

            logger.info("contract has open DDC -> exiting");

            skipped.put("skipped", true);
            return ResponseEntity.ok(skipped);
        }

        // ACC-7525 Contract has at least 1 closed DDC to-do in its log OR created the contract via CC payments
        if (!interModuleConnector.get("/sales/appsserviceddapprovaltodo/contracthastodo?" +
                "contract=" + contract.getId() + "&isClosed=true", Boolean.class)) {

            logger.info("Contract has not closed DDC -> exiting");

            skipped.put("skipped", true);
            return ResponseEntity.ok(skipped);
        }

        skipped.put("skipped", false);
        return ResponseEntity.ok(skipped);
    }

    @GetMapping("/createExpiryReminderFlow/{id}")
    @Transactional
    public ResponseEntity<?> createExpiryReminderFlow(
            @PathVariable(name = "id") Contract contract) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");

        Map<String, Object> lastSignatureType = directDebitSignatureService
                .getLastSignatureType(cpt, false, false);

        boolean useOldSignatures = ((Boolean) lastSignatureType.get("useApprovedSignature") ||
                (Boolean) lastSignatureType.get("useNonRejectedSignature"));

        Map<String, Object> additionalInfo = paymentExpiryService.generateDdsForPaymentExpiryFlow(
                cpt, monthlyPayment, useOldSignatures);

        paymentExpiryService.startPaymentExpiryFlow(cpt,
                new HashMap<String, Object>() {{
                    put("additionalInfo", additionalInfo);
                }},
                useOldSignatures && directDebitService.isRequiredBankInfoExist(cpt));

        return okResponse();
    }

    @GetMapping("/processExpiryReminderFlowTermination/{id}")
    @Transactional
    public ResponseEntity<?> processExpiryReminderFlowTermination(
            @PathVariable(name = "id") FlowProcessorEntity flow) {

        Map<String, Object> stopped = new HashMap<String, Object>() {{
            put("id", flow.getId());
        }};

        if (service.clientProvidesSignatureAndBankInfo(flow.getContract(), flow.getCreationDate())) {
            logger.info("Flow stopped because client provided signatures");

            flow.setCompleted(true);
            repository.save(flow);

            stopped.put("completed", true);
            return ResponseEntity.ok(stopped);
        }

        if (new DateTime().isAfter(new DateTime(flow.getContract().getPaidEndDate()))) {
            // ACC-8725
            // Check if the flow related with any Hidden Direct Debit -> Stop Termination of Contract
            if (flow.getRelatedDirectDebits()
                    .stream()
                    .anyMatch(DirectDebit::isHidden)) {

                logger.info("The Flow related with a Direct Debit Hidden -> exiting");
                return ResponseEntity.ok(stopped);
            }

            logger.info("Flow stopped because client didn't provide signatures");

            // Termination of Contract
            contractService.setContractForTermination(
                    flow.getContract(), flow.getFlowEventConfig().getCancellationReason().getCode());
            flow.setCausedTermination(true);
            flow.setStopped(true);
            repository.save(flow);

            stopped.put("stopped", true);
            return ResponseEntity.ok(stopped);
        }

        return ResponseEntity.ok(stopped);
    }
}
