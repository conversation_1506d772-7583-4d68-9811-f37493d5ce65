package com.magnamedia.controller.RiskDocumentsManagement;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementLayer;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementHistoryRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementLayerRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementVisitHistoryRepository;
import com.magnamedia.service.QueryService;
import com.magnamedia.service.RiskDocumentsManagement.RiskDocumentsManagementService;
import com.magnamedia.service.RiskDocumentsManagement.RiskDocumentsManagementToDoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/riskDocumentsManagement")
public class RiskDocumentsManagementController extends BaseRepositoryController<RiskDocumentsManagement> {

    @Autowired
    private RiskDocumentsManagementRepository riskDocumentsManagementRepository;
    @Autowired
    private RiskDocumentsManagementHistoryRepository riskDocumentsManagementHistoryRepository;
    @Autowired
    private RiskDocumentsManagementVisitHistoryRepository riskDocumentsManagementVisitHistoryRepository;
    @Autowired
    private RiskDocumentsManagementToDoService riskDocumentsManagementToDoService;
    @Autowired
    private RiskDocumentsManagementService riskDocumentsManagementService;
    @Autowired
    private RiskDocumentsManagementLayerRepository riskDocumentsManagementLayerRepository;

    @Override
    public BaseRepository<RiskDocumentsManagement> getRepository() {
        return riskDocumentsManagementRepository;
    }

    @PreAuthorize("hasPermission('riskDocumentsManagement','search')")
    @GetMapping(value = "/search/page/{id}")
    public ResponseEntity<?> list(
            @PathVariable(value = "id") String governmentEntity,
            Pageable pageable) {

         return ResponseEntity.ok(riskDocumentsManagementRepository
                 .getRiskDocumentsManagementByGovernmentEntity(governmentEntity, pageable));
    }

    @PreAuthorize("hasPermission('riskDocumentsManagement','getDocuments')")
    @PostMapping(value = "/getDocuments")
    public ResponseEntity<?> getDocuments(@RequestBody List<FilterItem> filters, Pageable pageable) {
        return ResponseEntity.ok(Setup.getApplicationContext()
                .getBean(QueryService.class)
                .riskDocumentsManagementSearch(pageable, filters));
    }

    @Override
    protected ResponseEntity<?> createEntity(RiskDocumentsManagement entity) {
        if (checkPermission("create")) {
            CurrentRequest.authorize();

            if (entity.getId() != null) {
                return new ResponseEntity<>(new Response("Id must be null"),
                        HttpStatus.BAD_REQUEST);
            }

            // Check for duplicate creationRenewalToDoDate in LAYER_TWO layers using anyMatch
            if (entity.getLayers() != null) {
                Set<java.sql.Date> layerTwoDates = new HashSet<>();

                // Use anyMatch to check for duplicates
                boolean hasDuplicate = entity.getLayers().stream()
                    .filter(layer -> layer.getType() == RiskDocumentsManagementLayer.Type.LAYER_TWO)
                    .anyMatch(layer -> !layerTwoDates.add(layer.getSendLayerTwoEmailDate()));

                if (hasDuplicate) {
                    throw new BusinessException("Two layers of type TWO have the same date");
                }
            }

            RiskDocumentsManagement document = (RiskDocumentsManagement) super.createEntity(entity).getBody();

            if (document == null) {
                throw new BusinessException("Failed to create document");
            }

            // Ensure proper relationship setup
            if (document.getLayers() != null) {
                for (RiskDocumentsManagementLayer layer : document.getLayers()) {
                    layer.setRiskDocumentsManagement(document);
                    riskDocumentsManagementLayerRepository.silentSave(layer);
                }
            }
            return ResponseEntity.ok(riskDocumentsManagementRepository.findOne(document.getId()));
        } else {
            return this.unauthorizedReponse();
        }
    }

    @Override
    @Transactional
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        if (this.checkPermission("update")) {
            CurrentRequest.authorize();
            RiskDocumentsManagement updated = this.parse(objectNode);
            RiskDocumentsManagement origin = riskDocumentsManagementRepository.findOne(updated.getId());

            riskDocumentsManagementService.UpdateDocumentExpiryDate(updated, origin);
            riskDocumentsManagementService.UpdateDocumentVisitDate(updated, origin);

            this.update(origin, updated, objectNode);

            origin.getAttachments().addAll(updated.getAttachments());
            this.updateEntity(origin);

            if (objectNode.has("attachments")) {
                // Get the attachments array from the ObjectNode
                JsonNode attachmentsNode = objectNode.get("attachments");

                // Convert to a list of Attachment objects
                List<Attachment> attachments = new ArrayList<>();
                if (attachmentsNode.isArray()) {
                    for (JsonNode attachmentNode : attachmentsNode) {
                        // Convert each node to an Attachment object
                        Attachment attachment = getObjectMapper().convertValue(attachmentNode, Attachment.class);
                        attachments.add(attachment);
                    }
                }
                origin.setAttachments(attachments);
                Storage.updateAttachements(origin);
            }

            return ResponseEntity.ok(riskDocumentsManagementRepository.save(origin));
        } else {
            return this.unauthorizedReponse();
        }
    }

    @PreAuthorize("hasPermission('riskDocumentsManagement','changeStatus')")
    @GetMapping(value = "/changeStatus/{id}")
    @Transactional
    public ResponseEntity<?> changeStatus(
            @PathVariable("id") RiskDocumentsManagement document,
            @RequestParam(name = "activate", defaultValue = "false") boolean activate) {

        riskDocumentsManagementService.closeDocument(document, activate);
        return okResponse();
    }

    @PreAuthorize("hasPermission('riskDocumentsManagement','getDocumentHistory')")
    @GetMapping(value = "/getDocumentHistory/{id}")
    public ResponseEntity<?> getDocumentHistory(@PathVariable("id") Long riskDocumentsManagementId,
                                                Pageable pageable) {
        return ResponseEntity.ok(riskDocumentsManagementHistoryRepository.getRiskDocumentsManagementHistoriesByRiskDocumentsManagement(riskDocumentsManagementId,
                pageable));
    }

    @PreAuthorize("hasPermission('riskDocumentsManagement','getDocumentVisitHistory')")
    @GetMapping(value = "/getDocumentVisitHistory/{id}")
    public ResponseEntity<?> getDocumentVisitHistory(@PathVariable("id") Long riskDocumentsManagementId,
                                                Pageable pageable) {
        return ResponseEntity.ok(riskDocumentsManagementVisitHistoryRepository
                .getRiskDocumentsManagementVisitHistoryByRiskDocumentsManagement(riskDocumentsManagementId,
                pageable));
    }

    @PreAuthorize("hasPermission('riskDocumentsManagement','updateCurrentPendingVisitInfo')")
    @PostMapping(value = "/updateCurrentPendingVisitInfo")
    public ResponseEntity<?> updateCurrentPendingVisitInfo(@RequestBody RiskDocumentsManagement entity) {
        RiskDocumentsManagement document = getRepository().findOne(entity.getId());
        document.setServiceReportName(entity.getServiceReportName());
        document.setFrequencyOfVisit(entity.getFrequencyOfVisit());
        document.setFrequencyType(entity.getFrequencyType());
        document.setNextVisitDate(entity.getNextVisitDate());
        document.setLastVisitDate(entity.getLastVisitDate());
        getRepository().save(document);
        return ResponseEntity.ok(document);
    }
}