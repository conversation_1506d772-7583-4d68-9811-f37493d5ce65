package com.magnamedia.controller.RiskDocumentsManagement;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.RiskDocumentsManagement.*;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementHistoryRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementLayerRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementToDoRepository;
import com.magnamedia.service.QueryService;
import com.magnamedia.service.RiskDocumentsManagement.RiskDocumentsManagementService;
import com.magnamedia.service.RiskDocumentsManagement.RiskDocumentsManagementVisitHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.swing.text.Document;
import java.net.URI;
import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/riskDocumentsManagementToDo")
public class RiskDocumentsManagementToDoController extends BaseRepositoryController<RiskDocumentsManagementTodo> {

    @Autowired
    private RiskDocumentsManagementToDoRepository riskManagementTodoRepository;
    @Autowired
    private RiskDocumentsManagementVisitHistoryService riskDocumentsManagementVisitHistoryService;
    @Autowired
    private RiskDocumentsManagementHistoryRepository riskDocumentsManagementHistoryRepository;
    @Autowired
    private RiskDocumentsManagementService riskDocumentsManagementService;
    @Override
    public BaseRepository<RiskDocumentsManagementTodo> getRepository() {
        return riskManagementTodoRepository;
    }

    @Override
    protected ResponseEntity<?> updateEntity(RiskDocumentsManagementTodo entity) {
        return super.updateEntity(entity);
    }


    @PreAuthorize("hasPermission('riskDocumentsManagementToDo','searchTodo')")
    @PostMapping(value = "/searchToDos")
    public ResponseEntity<?> searchToDos(Pageable pageable,
             @RequestBody(required = false) List<FilterItem> filters) {
        return ResponseEntity.ok(Setup.getApplicationContext()
                .getBean(QueryService.class)
                .riskDocumentsManagementTodosSearch(pageable, filters));
    }

    @PreAuthorize("hasPermission('riskDocumentsManagementToDo','setCurrentUserToUnderRenewalToDo')")
    @GetMapping(value = "/updateRiskDocumentsManagementTodoStatus/{id}")
    public ResponseEntity<?> updateRiskDocumentsManagementTodoStatus(
            @PathVariable("id") RiskDocumentsManagementTodo todo,
            @RequestParam RiskDocumentsManagementTodo.Status status) {

        todo.setStatus(status);
        if (status.equals(RiskDocumentsManagementTodo.Status.UNDER_RENEWAL) ||
                status.equals(RiskDocumentsManagementTodo.Status.RTA_TESTING)) {
            RiskDocumentsManagementTodo entity = getRepository().findOne(todo.getId());
            if (entity.getAssignee() != null) {
                throw new BusinessException("This todo is being handled by " + entity.getAssignee().getFullName());
            }
            todo.setAssignee(CurrentRequest.getUser());
        }
        riskManagementTodoRepository.save(todo);

        return okResponse();
    }

    @PreAuthorize("hasPermission('riskDocumentsManagementToDo','visitDone')")
    @PostMapping(value = "/visitDone")
    @Transactional
    public ResponseEntity<?> visitDone(@RequestBody RiskDocumentsManagementTodo todo) {

        RiskDocumentsManagementTodo entity = getRepository().findOne(todo.getId());
        entity.setStatus(RiskDocumentsManagementTodo.Status.CLOSED);
        entity.setNewVisitDate(todo.getNewVisitDate());
        entity.setDueDateForNextVisit(todo.getDueDateForNextVisit());
        entity.setAttachments(todo.getAttachments());

        RiskDocumentsManagement document = entity.getRiskDocumentsManagement();
        riskDocumentsManagementVisitHistoryService.addVisitHistoryForRelatedDocument(entity, document.getAttachments());

        document.setNextVisitDate(todo.getDueDateForNextVisit());
        document.setLastVisitDate(todo.getNewVisitDate());

        List<Attachment> attachments = new ArrayList<>();
        attachments.addAll(todo.getAttachments());
        attachments.addAll(document.getAttachments());
        document.setAttachments(attachments);

        Storage.updateAttachements(document);

        Setup.getRepository(RiskDocumentsManagementRepository.class)
                        .silentSave(document);

        getRepository().save(entity);
        return ResponseEntity.ok(true);
    }

    @PreAuthorize("hasPermission('riskDocumentsManagementToDo','doneRegistration')")
    @PostMapping(value = "/doneRegistration")
    @Transactional
    public ResponseEntity<?> doneRegistration(@RequestBody RiskDocumentsManagementTodo todo) {

        RiskDocumentsManagementTodo entity = getRepository().findOne(todo.getId());

        entity.setStatus(RiskDocumentsManagementTodo.Status.CLOSED);
        entity.setAttachments(todo.getAttachments());

        entity.setNewVehicleExpirationDate(todo.getNewVehicleExpirationDate());
        entity.setRegistrationDate(todo.getRegistrationDate());
        entity.setInsuranceExpirationDate(todo.getInsuranceExpirationDate());
        entity.setIssuanceDate(todo.getIssuanceDate());

        RiskDocumentsManagement document = entity.getRiskDocumentsManagement();

        // ACC-8589 modify to add history upon todo done registration
        RiskDocumentsManagementHistory riskDocumentsManagementHistory = new RiskDocumentsManagementHistory();
        riskDocumentsManagementHistory.setName(document.getName());
        riskDocumentsManagementHistory.setExpiryDate(document.getExpiryDate());
        riskDocumentsManagementHistory.setIssuanceDate(document.getIssuanceDate());
        riskDocumentsManagementHistory.setAttachments(document.getAttachments());
        riskDocumentsManagementHistory.setType(document.getType());
        riskDocumentsManagementHistory.setRegistrationDate(document.getRegistrationDate());
        riskDocumentsManagementHistory.setInsuranceExpirationDate(document.getInsuranceExpirationDate());

        List<RiskDocumentsManagement.DocumentRelatedTo> historyRelatedTos = riskDocumentsManagementHistory.getRelatesTo();
        historyRelatedTos.addAll(document.getRelatesTo());
        riskDocumentsManagementHistory.setRelatesTo(historyRelatedTos);

        List<RiskDocumentsManagementRole> historyAccPerson = riskDocumentsManagementHistory.getAccountablePerson();
        historyAccPerson.addAll(document.getAccountablePerson());
        riskDocumentsManagementHistory.setAccountablePerson(historyAccPerson);

        riskDocumentsManagementHistory.setRiskDocumentsManagement(entity.getRiskDocumentsManagement());
        riskDocumentsManagementHistoryRepository.save(riskDocumentsManagementHistory);

        // ACC-7580
        document.setExpiryDate(todo.getNewVehicleExpirationDate());
        document.setRegistrationDate(todo.getRegistrationDate());
        document.setInsuranceExpirationDate(todo.getInsuranceExpirationDate());
        document.setIssuanceDate(todo.getIssuanceDate());
        document.setStatus(RiskDocumentsManagement.Status.ACTIVE);

        List<Attachment> attachments = new ArrayList<>();
        attachments.addAll(todo.getAttachments());
        attachments.addAll(document.getAttachments());
        document.setAttachments(attachments);
        Storage.updateAttachements(document);

        Setup.getRepository(RiskDocumentsManagementRepository.class)
                .silentSave(document);

        getRepository().save(entity);

        riskDocumentsManagementService.updateDocumentLayersDates(document);
        return ResponseEntity.ok(true);
    }

    @PreAuthorize("hasPermission('riskDocumentsManagementToDo','doneRenewal')")
    @PostMapping(value = "/doneRenewal")
    @Transactional
    public ResponseEntity<?> doneRenewal(@RequestBody RiskDocumentsManagementTodo todo) {

        RiskDocumentsManagementTodo entity = getRepository().findOne(todo.getId());

        entity.setNewExpiryDate(todo.getNewExpiryDate());
        entity.setNewIssuanceDate(todo.getNewIssuanceDate());
        entity.setStatus(RiskDocumentsManagementTodo.Status.CLOSED);
        entity.setAttachments(todo.getAttachments());

        // set new creation renewal todo to layer
        RiskDocumentsManagementLayer layer = entity.getRiskDocumentsManagementLayer();
        LocalDate layerNewCreationRenewalToDate = todo.getNewExpiryDate().toLocalDate()
                .minusDays(layer.getDaysBeforeExpiry());
        layer.setCreationRenewalToDoDate(Date.valueOf(layerNewCreationRenewalToDate));

        entity.setRiskDocumentsManagementLayer(layer);

        // return document from todo.
        RiskDocumentsManagement document = entity.getRiskDocumentsManagement();

        // creation of risk document history.

        RiskDocumentsManagementHistory riskDocumentsManagementHistory = new RiskDocumentsManagementHistory();
        riskDocumentsManagementHistory.setName(document.getName());
        riskDocumentsManagementHistory.setExpiryDate(document.getExpiryDate());
        riskDocumentsManagementHistory.setIssuanceDate(document.getIssuanceDate());
        riskDocumentsManagementHistory.setAttachments(document.getAttachments());
        riskDocumentsManagementHistory.setType(document.getType());

        List<RiskDocumentsManagement.DocumentRelatedTo> historyRelatedTos = riskDocumentsManagementHistory.getRelatesTo();
        historyRelatedTos.addAll(document.getRelatesTo());
        riskDocumentsManagementHistory.setRelatesTo(historyRelatedTos);

        List<RiskDocumentsManagementRole> historyAccPerson = riskDocumentsManagementHistory.getAccountablePerson();
        historyAccPerson.addAll(document.getAccountablePerson());
        riskDocumentsManagementHistory.setAccountablePerson(historyAccPerson);
        riskDocumentsManagementHistory.setRiskDocumentsManagement(entity.getRiskDocumentsManagement());
        riskDocumentsManagementHistoryRepository.save(riskDocumentsManagementHistory);

        // save document info
        document.setExpiryDate(todo.getNewExpiryDate());
        document.setIssuanceDate(todo.getNewIssuanceDate());
        document.setStatus(RiskDocumentsManagement.Status.ACTIVE);

        List<Attachment> attachments = new ArrayList<>();
        attachments.addAll(todo.getAttachments());
        attachments.addAll(document.getAttachments());
        document.setAttachments(attachments);
        Storage.updateAttachements(document);

        Setup.getRepository(RiskDocumentsManagementRepository.class).silentSave(document);
        getRepository().save(entity);

        riskDocumentsManagementService.updateDocumentLayersDates(document);
        riskDocumentsManagementService.sendRenewalEmail(document);

        return okResponse();
    }
}