package com.magnamedia.controller.RiskDocumentsManagement;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementHistory;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementHistoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/riskDocumentsManagementHistory")
public class RiskDocumentsManagementHistoryController extends BaseRepositoryController<RiskDocumentsManagementHistory> {

    @Autowired
    private RiskDocumentsManagementHistoryRepository riskDocumentsManagementHistoryRepository;

    @Override
    public BaseRepository<RiskDocumentsManagementHistory> getRepository() {
        return riskDocumentsManagementHistoryRepository;
    }
}
