package com.magnamedia.controller.RiskDocumentsManagement;


import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementRole;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementRoleRepository;
import com.magnamedia.service.RiskDocumentsManagement.RiskDocumentsManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/riskDocumentsManagementRole")
public class RiskDocumentsManagementRoleController extends BaseRepositoryController<RiskDocumentsManagementRole> {

    @Autowired
    private RiskDocumentsManagementRoleRepository riskManagementRoleRepository;
    @Autowired
    private RiskDocumentsManagementService riskDocumentsManagementService;

    @Override
    public BaseRepository<RiskDocumentsManagementRole> getRepository() {
        return riskManagementRoleRepository;
    }

    @PreAuthorize("hasPermission('riskDocumentsManagementRole','deleteRole')")
    @GetMapping("/deleteRole/{id}")
    protected ResponseEntity<?> deleteRole(@PathVariable("id") RiskDocumentsManagementRole entity) {
        riskDocumentsManagementService.processDeletedRoleDocument(entity);
        riskDocumentsManagementService.processDeletedRoleLayer(entity);

        entity.setDeleted(true);
        getRepository().save(entity);
        return okResponse();
    }

    @PreAuthorize("hasPermission('riskDocumentsManagementRole','getActiveRoles')")
    @GetMapping("/getActiveRoles")
    protected ResponseEntity<?> getActiveRoles(Pageable pageable) {
        return ResponseEntity.ok(riskManagementRoleRepository.getActiveRoles(pageable));
    }
}