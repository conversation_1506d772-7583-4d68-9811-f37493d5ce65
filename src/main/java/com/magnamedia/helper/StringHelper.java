package com.magnamedia.helper;

import java.util.Random;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 3, 2019
 * ACC-837
 */
public class StringHelper {

    static final String AB = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    static Random rnd = new Random();

    public static String randomString(int len) {
        StringBuilder sb = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            sb.append(AB.charAt(rnd.nextInt(AB.length())));
        }
        return sb.toString();
    }

    public static String NormalizePhoneNumber(String number) {

        String NormalizedNumber = number;
        NormalizedNumber = NormalizedNumber.replace("-", "");
        NormalizedNumber = NormalizedNumber.replace("+", "");
        NormalizedNumber = NormalizedNumber.replace(")", "");
        NormalizedNumber = NormalizedNumber.replace("(", "");
        NormalizedNumber = NormalizedNumber.replace(" ", "");

        NormalizedNumber = removeFirst(NormalizedNumber, "00");
        
        if (NormalizedNumber.startsWith("0"))
        {
          NormalizedNumber = removeFirst(NormalizedNumber, "0");
          NormalizedNumber = "971" + NormalizedNumber;
        }
        if (NormalizedNumber.startsWith("9710"))
        {
          NormalizedNumber = removeFirst(NormalizedNumber, "9710");
          NormalizedNumber = "971" + NormalizedNumber;
        }
        return NormalizedNumber;
    }

    public static String removeFirst(String s,
            String toRemove) {
        int x = 0;
        if (s.startsWith(toRemove)) {
            s = s.substring(toRemove.length());
        }
        return s;
    }
}
