package com.magnamedia.helper;

import com.magnamedia.core.entity.BaseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.logging.Logger;

/**
 * Generic pagination utility for scheduled jobs that need to process large datasets
 * using cursor-based pagination with lastId pattern.
 * <p>
 * This helper eliminates code duplication across scheduled jobs by providing
 * a reusable pagination mechanism that works with any entity type.
 *
 * <AUTHOR>
 * @created 02/06/2025 - 11:30 AM
 * <p>
 * ACC-9459
 */
public class PaginationHelper {

    private static final Logger logger = Logger.getLogger(PaginationHelper.class.getName());

    /**
     * Functional interface for fetching paginated data from repository
     *
     * @param <T> The entity type being paginated
     */
    @FunctionalInterface
    public interface QueryPageFetcher<T> {
        /**
         * Fetch a page of data starting from the given lastId
         *
         * @param lastId The ID of the last processed entity (for cursor-based pagination)
         * @param pageRequest The page request containing page size and other pagination info
         * @return Page of entities
         */
        Page<T> fetchPage(Long lastId, PageRequest pageRequest);
    }

    /**
     * Functional interface for processing individual entities
     *
     * @param <T> The entity type being processed
     */
    @FunctionalInterface
    public interface EntityProcessor<T> {
        /**
         * Process a single entity
         *
         * @param entity The entity to process
         * @throws Exception if processing fails
         */
        void process(T entity) throws Exception;
    }

    /**
     * Process entities using cursor-based pagination with default page size
     *
     * @param <T> The entity type being processed
     * @param queryPageFetcher Function to fetch paginated data
     * @param entityProcessor Function to process each entity
     * @param jobName Name of the job for logging purposes
     */
    public static <T> void processPaginated(
            String jobName,
            QueryPageFetcher<T> queryPageFetcher,
            EntityProcessor<T> entityProcessor) {

        processPaginated(jobName, queryPageFetcher, entityProcessor, 200);
    }

    /**
     * Process entities using cursor-based pagination with custom page size
     *
     * @param <T> The entity type being processed
     * @param queryPageFetcher Function to fetch paginated data
     * @param entityProcessor Function to process each entity
     * @param jobName Name of the job for logging purposes
     * @param pageSize Number of entities to process per page
     */
    public static <T> void processPaginated(
            String jobName,
            QueryPageFetcher<T> queryPageFetcher,
            EntityProcessor<T> entityProcessor,
            int pageSize) {

        logger.info("Starting pagination processing for job: " + jobName);

        Page<T> page;
        Long lastId = -1L;
        int pageCount = 0;
        int totalProcessed = 0;

        do {
            pageCount++;
            page = queryPageFetcher.fetchPage(lastId, PageRequest.of(0, pageSize));

            logger.info(String.format("Job: %s, Page: %d, Size: %d, LastId: %d",
                    jobName, pageCount, page.getContent().size(), lastId));

            // Process each entity in the current page
            page.getContent()
                    .forEach(entity -> {
                        try {
                            entityProcessor.process(entity);
                        } catch (Exception e) {
                            logger.severe(String.format("Job: %s, Error processing entity with ID: %d, Error: %s",
                                    jobName, ((BaseEntity) entity).getId() , e.getMessage()));
                            e.printStackTrace();
                        }
                    });

            // Update lastId for next iteration if there are more entities
            if (!page.getContent().isEmpty()) {
                lastId = ((BaseEntity) page.getContent().get(page.getContent().size() - 1)).getId();
                totalProcessed += page.getContent().size();
            }

        } while (!page.getContent().isEmpty());

        logger.info(String.format("Completed pagination processing for job: %s, Pages: %d, Total processed: %d",
                jobName, pageCount, totalProcessed));
    }
}