package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.module.type.*;
import com.magnamedia.service.DDMessagingService;
import com.magnamedia.service.QueryService;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.sql.Time;
import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *         Created on Apr 13, 2020
 *         Jirra ACC-1611
 */
@Entity
@Where(clause = "DELETED <> true")
public class DDMessaging extends DDMessagingContract {
    private static final Logger logger = Logger.getLogger(DDMessaging.class.getName());

    @Column(columnDefinition = "boolean default true")
    private Boolean isActive = true;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private DDMessagingType event;


    @Column
    private String ddCategory;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitRejectCategory rejectCategory;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitDataEntryRejectCategory dataEntryRejectCategory;


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem bouncedPaymentStatus;

    @Column
    private String contractProspectTypes;

    @Column
    private String trials;

    @Column
    private String reminders;

    //Jirra ACC-3180
    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitMessagingWhenToStartSending whenToStartSending;

    @Column
    private Long afterDays;

    @Column
    private Time sendTime;

    @Column
    @Enumerated(EnumType.STRING)
    private DirectDebitMessagingScheduleTermCategory scheduleTermCategory;

    @Column(columnDefinition = "boolean default false")
    private Boolean createHumanSms = false;

    @Column
    private String humanSmsTitle;

    @Column
    private String humanSmsDescription;

    @Column
    private Time humanSmsTime;

    @Column
    private String parameterToUsed;

    @Transient
    @Basic(fetch = FetchType.LAZY)
    private String smsText;

    @Column
    @Enumerated(EnumType.STRING)
    private ClientMessagePriority clientMessagePriority;

    @Column
    @Enumerated(EnumType.STRING)
    private DDMessagingSubType subType;

    @Column(columnDefinition = "boolean default false")
    private Boolean sendPayTabMessage = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean alertForVip = false;

    @Transient
    @JsonIgnore
    private java.sql.Date sendDate;

    @Column(columnDefinition = "boolean default false")
    private Boolean deleted = false;

    @Column(columnDefinition = "boolean default false")
    private boolean createClientToDo = false;

    @Column
    @Enumerated(EnumType.STRING)
    private DDMessagingPaymentStructure paymentStructure;

    public Boolean getActive() {
        return isActive;
    }

    public void setActive(Boolean active) {
        isActive = active;
    }

    public DDMessagingType getEvent() {
        return event;
    }

    public void setEvent(DDMessagingType event) {
        this.event = event;
    }

    public String getDdCategory() {
        return ddCategory;
    }

    public void setDdCategory(String ddCategory) {
        this.ddCategory = ddCategory;
    }

    public DirectDebitRejectCategory getRejectCategory() {
        return rejectCategory;
    }

    public void setRejectCategory(DirectDebitRejectCategory rejectCategory) {
        this.rejectCategory = rejectCategory;
    }

    public String getTrials() {
        return trials;
    }

    public void setTrials(String trials) {
        this.trials = trials;
    }

    public String getReminders() {
        return reminders;
    }

    public void setReminders(String reminders) {
        this.reminders = reminders;
    }

    public Long getAfterDays() {
        return afterDays;
    }

    public void setAfterDays(Long afterDays) {
        this.afterDays = afterDays;
    }

    public DirectDebitMessagingWhenToStartSending getWhenToStartSending() {
        return whenToStartSending;
    }

    public void setWhenToStartSending(DirectDebitMessagingWhenToStartSending whenToStartSending) {
        this.whenToStartSending = whenToStartSending;
    }

    public Time getSendTime() {
        return sendTime;
    }

    public void setSendTime(Time sendTime) {
        this.sendTime = sendTime;
    }

    public DirectDebitMessagingScheduleTermCategory getScheduleTermCategory() {
        return scheduleTermCategory;
    }

    public void setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory scheduleTermCategory) {
        this.scheduleTermCategory = scheduleTermCategory;
    }

    public Boolean getCreateHumanSms() {
        return createHumanSms;
    }

    public void setCreateHumanSms(Boolean createHumanSms) {
        this.createHumanSms = createHumanSms;
    }

    public String getHumanSmsTitle() {
        return humanSmsTitle;
    }

    public void setHumanSmsTitle(String humanSmsTitle) {
        this.humanSmsTitle = humanSmsTitle;
    }

    public String getHumanSmsDescription() {
        return humanSmsDescription;
    }

    public void setHumanSmsDescription(String humanSmsDescription) {
        this.humanSmsDescription = humanSmsDescription;
    }

    public Time getHumanSmsTime() {
        return humanSmsTime;
    }

    public void setHumanSmsTime(Time humanSmsTime) {
        this.humanSmsTime = humanSmsTime;
    }

    public String getParameterToUsed() {
        return parameterToUsed;
    }

    public void setParameterToUsed(String parameterToUsed) {
        this.parameterToUsed = parameterToUsed;
    }

    public PicklistItem getBouncedPaymentStatus() {
        return bouncedPaymentStatus;
    }

    public void setBouncedPaymentStatus(PicklistItem bouncedPaymentStatus) {
        this.bouncedPaymentStatus = bouncedPaymentStatus;
    }

    public String getSmsText() {

        if(this.smsText != null) return this.smsText;

        if(getClientTemplate() == null ||
                !getClientTemplate().isChannelExist(ChannelSpecificSettingType.SMS)) return "";

        
        return getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS.toString()).getText();
    }

    public String getContractProspectTypes() {
        return contractProspectTypes;
    }

    public void setContractProspectTypes(String contractProspectTypes) {
        this.contractProspectTypes = contractProspectTypes;
    }

    public DirectDebitDataEntryRejectCategory getDataEntryRejectCategory() {
        return dataEntryRejectCategory;
    }

    public void setDataEntryRejectCategory(DirectDebitDataEntryRejectCategory dataEntryRejectCategory) {
        this.dataEntryRejectCategory = dataEntryRejectCategory;
    }

    public DDMessagingSubType getSubType() { return subType; }

    public void setSubType(DDMessagingSubType subType) { this.subType = subType; }

    public Boolean getSendPayTabMessage() { return sendPayTabMessage != null && sendPayTabMessage; }

    public void setSendPayTabMessage(Boolean sendPayTabMessage) { this.sendPayTabMessage = sendPayTabMessage; }

    public Boolean getAlertForVip() { return alertForVip; }

    public void setAlertForVip(Boolean alertForVip) { this.alertForVip = alertForVip; }

    public java.sql.Date getSendDate() { return sendDate; }

    public void setSendDate(java.sql.Date sendDate) { this.sendDate = sendDate; }

    @BeforeInsert
    @BeforeUpdate
    private void beforeInsertOrUpdate() {
        createMessageTemplateIfNeeded(this);
    }

    public Long checkEntity() {
        DDMessaging entity = this;

        if (entity.getEvent() == null) throw new RuntimeException("msg event must be selected");

        if (entity.getContractProspectTypes() == null) throw new RuntimeException("contract type must be selected");

        if (!entity.getContractProspectTypes().equalsIgnoreCase("maids.cc_prospect")
                && !entity.getContractProspectTypes().equalsIgnoreCase("maidvisa.ae_prospect")
                && !entity.getContractProspectTypes().equalsIgnoreCase("maidvisa.ae_prospect,maids.cc_prospect")
                && !entity.getContractProspectTypes().equalsIgnoreCase("maids.cc_prospect,maidvisa.ae_prospect")) {
            throw new RuntimeException("contract type values must one of (maids.cc_prospect,maidvisa.ae_prospect)");
        }

        switch (entity.getEvent()) {
            case DirectDebitRejected:
                if (entity.getDdCategory() == null)
                    throw new RuntimeException("dd type must be selected");
                if (!entity.getDdCategory().equalsIgnoreCase("A")
                        && !entity.getDdCategory().equalsIgnoreCase("B")
                        && !entity.getDdCategory().equalsIgnoreCase("A,B")) {
                    throw new RuntimeException("dd type values must be A or B or A,B");
                }   
                if (entity.getRejectCategory() == null)
                    throw new RuntimeException("dd reject category must be selected");
                if (entity.getTrials() == null || entity.getReminders() == null)
                    throw new RuntimeException("entity & reminders must be selected");
                if (entity.getBouncedPaymentStatus() != null)
                    throw new RuntimeException("bounced payment status must not be selected");
                if (entity.getAfterDays() != null)
                    entity.setAfterDays(null);
                break;
            case ClientPaidCashAndNoSignatureProvided:
                if (entity.getDdCategory() != null)
                    throw new RuntimeException("dd type must not be selected");
                if (entity.getRejectCategory() != null)
                    throw new RuntimeException("dd reject category must not be selected");
                if (entity.getBouncedPaymentStatus() != null)
                    throw new RuntimeException("bounced payment status must not be selected");
                entity.setReminders("1");
                break;
            case IncompleteDDRejectedByDataEntry:
                if (entity.getDataEntryRejectCategory() == null)
                    throw new RuntimeException("dataEntryRejectCategory must be selected");
                if (entity.getTrials() == null || entity.getReminders() == null)
                    throw new RuntimeException("entity & reminders must be selected");
                if (entity.getBouncedPaymentStatus() != null)
                    throw new RuntimeException("bounced payment status must not be selected");
                if (entity.getAfterDays() != null)
                    entity.setAfterDays(null);
                break;
            case BouncedPayment:
                if (entity.getDdCategory() != null)
                    throw new RuntimeException("dd type must not be selected");
                if (entity.getRejectCategory() != null)
                    throw new RuntimeException("dd reject category must not be selected");
                if (entity.getTrials() == null || entity.getReminders() == null)
                    throw new RuntimeException("entity & reminders must be selected");
                if (entity.getBouncedPaymentStatus() == null)
                    throw new RuntimeException("bounced payment status must be selected");
                if (!entity.getBouncedPaymentStatus().getCode().equals("bounced_payment_received")) {
                    List<Template> templates = new ArrayList();
                    if (getClientTemplate() != null) templates.add(getClientTemplate());
                    if (getMaidTemplate() != null) templates.add(getMaidTemplate());
                    
                    if (isAnyTemplateContains(templates, "@soonest_payroll@")) {
                        throw new RuntimeException(String.format("Bounced Payment with Status '%s' can't have '%s' parameter", entity.getBouncedPaymentStatus().getName(), "soonest_payroll"));
                    }
                }   
                if (entity.getAfterDays() != null)
                    entity.setAfterDays(null);
                break;
            case IncompleteDDClientHasNoApprovedSignature:
                if (entity.getTrials() == null || entity.getReminders() == null)
                    throw new RuntimeException("trials & reminders must be selected");
                if (entity.getAfterDays() != null)
                    entity.setAfterDays(null);
                break;
            case OnlineCreditCardPaymentReminders:
                if (entity.getReminders() == null)
                    throw new RuntimeException("reminders must be selected");
                if (entity.getPaymentStructure() == null)
                    throw new RuntimeException("payment structure must be selected");
                if (entity.getSubType() == null)
                    throw new RuntimeException("sub type must be selected");
                entity.setTrials("1");
                break;
            case ClientsPayingViaCreditCard:
                if (entity.getTrials() == null)
                    throw new RuntimeException("trials must be selected");
                if (entity.getSubType() == null)
                    throw new RuntimeException("sub type must be selected");
                if (entity.getSubType().equals(DDMessagingSubType.DD_Rejection) && entity.getRejectCategory() == null)
                    throw new RuntimeException("dd reject category must not be selected");
                if (Arrays.asList(DDMessagingSubType.INITIAL_FLOW_FOR_DDA, DDMessagingSubType.INITIAL_FLOW_FOR_DDB)
                        .contains(entity.getSubType()) && entity.getPaymentStructure() == null)
                    throw new RuntimeException("payment structure must be selected");
                entity.setReminders("1");
                break;
//            case OneMonthAgreement:
//                if (entity.getTrials() == null)
//                    throw new RuntimeException("trials must be selected");
//                if (entity.getSubType() == null)
//                    throw new RuntimeException("sub type must be selected");
//                entity.setReminders("1");
//                break;
            default:
                break;
        }

        /*if (entity.getSendToClient() || entity.getSendToSpouse())
            if (entity.getClientMessage() == null || entity.getClientMessage().equalsIgnoreCase(""))
                throw new RuntimeException("client message can't be empty");*/

        if (entity.getSendAsEmail())
            if (entity.getEmailSubject() == null || entity.getEmailSubject().equalsIgnoreCase(""))
                throw new RuntimeException("email subject can't be empty");

        if (entity.getSendToMaid())
            if (entity.getMaidMessage() == null || entity.getMaidMessage().equalsIgnoreCase(""))
                throw new RuntimeException("maid message can't be empty");

        if (entity.getSendToMaidWhenRetractCancellation())
            if (entity.getMaidWhenRetractCancellationMessage() == null || entity.getMaidWhenRetractCancellationMessage().equalsIgnoreCase("")
                    || entity.getScheduleTermCategory().equals(DirectDebitMessagingScheduleTermCategory.None))
                throw new RuntimeException("maid message can't be empty and scheduled date of termination should be greater than or equal today");

        /*if (entity.getCreateHumanSms()) {
            if (entity.getHumanSmsTitle() == null || entity.getHumanSmsTitle().equalsIgnoreCase(""))
                throw new RuntimeException("human sms title can't be empty");
        }*/

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", entity.getEvent());
        switch (entity.getEvent()) {
            case BouncedPayment:
                query.filterBy("bouncedPaymentStatus", "=", entity.getBouncedPaymentStatus());
                query.filterBy("trials", "like", "%" + entity.getTrials() + "%");
                query.filterBy("reminders", "like", "%" + entity.getReminders() + "%");
                break;

            case ClientPaidCashAndNoSignatureProvided:
                query.filterBy("whenToStartSending", "=", entity.getWhenToStartSending());
                query.filterBy("trials", "like", "%" + entity.getTrials() + "%");
                query.filterBy("sendPayTabMessage", "=", entity.getSendPayTabMessage());
                break;
            case IncompleteDDRejectedByDataEntry:
                query.filterBy("dataEntryRejectCategory", "=", entity.getDataEntryRejectCategory());
                query.filterBy("trials", "like", "%" + entity.getTrials() + "%");
                query.filterBy("reminders", "like", "%" + entity.getReminders() + "%");
                break;

            case DirectDebitRejected:
                query.filterBy("ddCategory", "like", "%" + entity.getDdCategory() + "%");
                query.filterBy("rejectCategory", "=", entity.getRejectCategory());
                query.filterBy("trials", "like", "%" + entity.getTrials() + "%");
                query.filterBy("reminders", "like", "%" + entity.getReminders() + "%");
                break;

            case IncompleteDDClientHasNoApprovedSignature:
                query.filterBy("trials", "like", "%" + entity.getTrials() + "%");
                query.filterBy("reminders", "like", "%" + entity.getReminders() + "%");
                break;
            case OnlineCreditCardPaymentReminders:
                query.filterBy("reminders", "like", "%" + entity.getReminders() + "%");
                query.filterBy("subType", "=", getSubType());
                query.filterBy("paymentStructure", "=", getPaymentStructure());
                break;
            case ClientsPayingViaCreditCard:
                query.filterBy("trials", "like", "%" + entity.getTrials() + "%");
                query.filterBy("subType", "=", entity.getSubType());
                if (entity.getSubType().equals(DDMessagingSubType.DD_Rejection))
                    query.filterBy("rejectCategory", "=", entity.getRejectCategory());
                if (Arrays.asList(DDMessagingSubType.INITIAL_FLOW_FOR_DDA, DDMessagingSubType.INITIAL_FLOW_FOR_DDB)
                        .contains(entity.getSubType())) {
                    query.filterBy("paymentStructure", "=", getPaymentStructure());
                }
                break;
//            case OneMonthAgreement:
//                query.filterBy("trials", "like", "%" + entity.getTrials() + "%");
//                query.filterBy("subType", "=", entity.getSubType());
//                break;
        }
        query.filterBy("contractProspectTypes", "like", "%" + entity.getContractProspectTypes() + "%");
        query.filterBy("scheduleTermCategory", "=", entity.getScheduleTermCategory());

        if (entity.getId() != null) query.filterBy("id", "<>", entity.getId());

        List<DDMessaging> messages = query.execute();
        if (messages.isEmpty()) return null;

        return messages.get(0).getId();
    }

    private boolean isAnyTemplateContains(List<Template> templates, String text) {
        for (Template template : templates) {
            if (isTemplateContains(template, text)) return true;
        }

        return false;
    }

    private boolean isTemplateContains(Template template, String text) {

        return (template.isChannelExist(ChannelSpecificSettingType.Notification) &&
                template.getChannelSetting(ChannelSpecificSettingType.Notification).getText().contains(text)) ||
                (template.isChannelExist(ChannelSpecificSettingType.SMS) &&
                        template.getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains(text));
    }

    private void createMessageTemplateIfNeeded(DDMessaging entity) {
        DDMessagingService service = Setup.getApplicationContext().getBean(DDMessagingService.class);

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", entity.getEvent());
        List<DDMessaging> ddMessagingList = query.execute();

        long oldClientDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getClientTemplate() != null)
                .count() + 1;
        long oldMaidDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getMaidTemplate() != null)
                .count() + 1;
        long oldMaidRetractDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getMaidWhenRetractCancellationTemplate() != null)
                .count() + 1;

        Map<String, Object> m = new HashMap<String, Object>() {{
            put("sendToClient", getSendToClient());
            put("sendToSpouse", getSendToSpouse());
            put("sendAsEmail", getSendAsEmail());
            put("clientTemplate", getClientTemplate());
            put("clientMessage", getClientMessage());
            put("smsText", entity.getClientSmsMessage() == null ?
                    entity.getClientMessage() : entity.getClientSmsMessage());
            put("notificationTemplateName", service.fetchTemplateName(
                    "Accounting_dd_messaging_setup_client_notification_" + entity.getEvent().toString() + "_",
                    oldClientDdMessagingCount));

            put("sendToMaid", getSendToMaid());
            put("maidTemplate", getMaidTemplate());
            put("maidMessage", getMaidMessage());
            put("maid_k", service.fetchTemplateName(
                    "Accounting_dd_messaging_setup_maid_notification_" + entity.getEvent().toString() + "_",
                    oldMaidDdMessagingCount));

            put("sendToMaidWhenRetractCancellation", getSendToMaidWhenRetractCancellation());
            put("maidWhenRetractCancellationTemplate", getMaidWhenRetractCancellationTemplate());
            put("maidWhenRetractCancellationMessage", getMaidWhenRetractCancellationMessage());
            put("maidRetract_k", service.fetchTemplateName(
                    "Accounting_dd_messaging_setup_maid_notification_retractCancellation_" + entity.getEvent().toString() + "_",
                    oldMaidRetractDdMessagingCount));

            put("contractProspectTypes", getContractProspectTypes());
        }};

        Map<String, Object> r = service.createMessageTemplateIfNeeded(m);

        if (r.containsKey("clientTemplate"))
            entity.setClientTemplate((Template) r.get("clientTemplate"));
        if (r.containsKey("maidTemplate"))
            entity.setMaidTemplate((Template) r.get("maidTemplate"));
        if (r.containsKey("maidWhenRetractCancellationTemplate"))
            entity.setMaidWhenRetractCancellationTemplate((Template) r.get("maidWhenRetractCancellationTemplate"));
    }


    public ClientMessagePriority getClientMessagePriority() { return clientMessagePriority; }

    public void setClientMessagePriority(ClientMessagePriority clientMessagePriority) {
        this.clientMessagePriority = clientMessagePriority;
    }

    public Boolean getDeleted() { return deleted; }

    public void setDeleted(Boolean deleted) { this.deleted = deleted; }

    private void createAlLowedParameterBGT(Template t, String joinColumn) {

        Setup.getApplicationContext()
                .getBean(DDMessagingService.class)
                .createAllowedParametersForTemplate(t,
                        Setup.getApplicationContext()
                        .getBean(QueryService.class)
                        .getDdMessagingParametersBasedOnEventAndSubEvent(getEvent(), getSubType(), joinColumn));

    }

    @JsonIgnore
    @Override
    public DDMessaging getDdMessaging() { return this; }

    public boolean isCreateClientToDo() { return createClientToDo; }

    public void setCreateClientToDo(boolean createClientToDo) { this.createClientToDo = createClientToDo; }

    public DDMessagingPaymentStructure getPaymentStructure() { return paymentStructure; }

    public void setPaymentStructure(DDMessagingPaymentStructure paymentStructure) { this.paymentStructure = paymentStructure; }
}