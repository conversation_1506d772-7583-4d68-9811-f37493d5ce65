package com.magnamedia.entity.projection;

import com.magnamedia.module.type.BankTransactionMatchType;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * Created At 4/22/2025
 **/
public interface PayrollTransferAlreadyMatchedTransactionProjection {

    Long getId();

    @Value("#{target.getDate() != null ? new org.joda.time.LocalDate(target.getDate()).toString('yyyy-MM-dd') : null}")
    String getDate();

    Double getTransactionAmount();

    @Value("#{target.getBeneficiaryName()}")
    String getEmployeeName();

    @Value("#{target.getExpense() != null ? target.getExpense().getNameLabel() : ''}")
    String getExpenseType();

    String getFromBucket();

    String getDescription();

    String getPaymentDetail();

    BankTransactionMatchType getBankTransactionMatchType();
}