package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

public interface InvoiceStatementTransactionMatchedCSVProjection {


    @Value("#{target.visitDate}")
    Date getDate();

    @Value("#{target.expenseRequestTodo != null && target.expenseRequestTodo.expense != null ? target.expenseRequestTodo.expense.name : ''}")
    String getExpenseName();


    @Value("#{target.expenseRequestTodo != null ? (target.expenseRequestTodo.relatedToType != null ? target.expenseRequestTodo.relatedToType.toString() : '') : ''}")
    String getRelatedTo();

    @Value("#{target.housemaid != null && target.housemaid.passportNumber != null ? target.housemaid.passportNumber : (target.passportNo != null ? target.passportNo : '')}")
    String getMaidPassportNumber();

    Double getAmount();
}
