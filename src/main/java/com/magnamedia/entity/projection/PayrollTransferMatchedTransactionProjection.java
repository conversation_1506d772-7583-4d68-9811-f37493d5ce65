package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * Created At 4/22/2025
 **/
public interface PayrollTransferMatchedTransactionProjection {

    Long getId();

    Double getTransactionAmount();

    @Value("#{target.getBeneficiaryName()}")
    String getEmployeeName();

    @Value("#{target.getExpense() != null ? target.getExpense().getNameLabel() : ''}")
    String getExpenseType();

    String getFromBucket();

    String getDescription();

    String getPaymentDetail();
}