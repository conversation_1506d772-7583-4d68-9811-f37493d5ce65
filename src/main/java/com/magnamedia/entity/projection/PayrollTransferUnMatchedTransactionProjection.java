package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * Created At 4/22/2025
 **/
public interface PayrollTransferUnMatchedTransactionProjection {

    Long getId();

    Double getTransactionAmount();

    String getBeneficiaryName();

    String getReason();

    String getDescription();

    String getPaymentDetail();

    String getNote();

    @Value("#{target.getDate() != null ? new org.joda.time.LocalDate(target.getDate()).toString('yyyy-MM-dd') : null}")
    String getDate();
}