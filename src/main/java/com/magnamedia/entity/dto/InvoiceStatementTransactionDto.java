package com.magnamedia.entity.dto;

import com.magnamedia.extra.InvoiceStatementTransactionType;

import java.util.Date;

/**
 * DTO for Invoice Statement Transaction with detailed information
 */
public class InvoiceStatementTransactionDto {

    private Long id;
    private Date visitDate;
    private String expenseName;
    private String relatedTo;
    private String maidPassportNo;
    private String maidType;
    private Double amount;
    private String reason;
    private InvoiceStatementTransactionType type;
    private Long expenseRequestTodoId;

    public InvoiceStatementTransactionDto() {
    }

    public InvoiceStatementTransactionDto(Long id, Date visitDate, String expenseName,
                                        String relatedTo, String maidPassportNo,
                                        String maidType, Double amount,
                                        String reason, InvoiceStatementTransactionType type,
                                        Long expenseRequestTodoId) {
        this.id = id;
        this.visitDate = visitDate;
        this.expenseName = expenseName;
        this.relatedTo = relatedTo;
        this.maidPassportNo = maidPassportNo;
        this.maidType = maidType;
        this.amount = amount;
        this.reason = reason;
        this.type = type;
        this.expenseRequestTodoId = expenseRequestTodoId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getVisitDate() {
        return visitDate;
    }

    public void setVisitDate(Date visitDate) {
        this.visitDate = visitDate;
    }

    public String getExpenseName() {
        return expenseName;
    }

    public void setExpenseName(String expenseName) {
        this.expenseName = expenseName;
    }

    public String getRelatedTo() {
        return relatedTo;
    }

    public void setRelatedTo(String relatedTo) {
        this.relatedTo = relatedTo;
    }

    public String getMaidPassportNo() {
        return maidPassportNo;
    }

    public void setMaidPassportNo(String maidPassportNo) {
        this.maidPassportNo = maidPassportNo;
    }

    public String getMaidType() {
        return maidType;
    }

    public void setMaidType(String maidType) {
        this.maidType = maidType;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public InvoiceStatementTransactionType getType() {
        return type;
    }

    public void setType(InvoiceStatementTransactionType type) {
        this.type = type;
    }

    public Long getExpenseRequestTodoId() {
        return expenseRequestTodoId;
    }

    public void setExpenseRequestTodoId(Long expenseRequestTodoId) {
        this.expenseRequestTodoId = expenseRequestTodoId;
    }
}
