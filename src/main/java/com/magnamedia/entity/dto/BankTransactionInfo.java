package com.magnamedia.entity.dto;

import com.magnamedia.entity.BankStatementTransaction;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class BankTransactionInfo {
    private BankStatementTransaction transaction;
    private List<OfficeStaffPayrollLog> logs = new ArrayList<>();
    private Expense expense;

    public BankTransactionInfo(BankStatementTransaction transaction, Expense expense) {
        this.transaction = transaction;
        this.expense = expense;
    }

    public void addLog(OfficeStaffPayrollLog log) {
        if (log != null) this.logs.add(log);
    }
}