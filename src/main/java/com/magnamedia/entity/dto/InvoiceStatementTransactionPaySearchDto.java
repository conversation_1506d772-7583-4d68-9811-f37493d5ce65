package com.magnamedia.entity.dto;

import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * DTO for searching Invoice Statement Transactions for payment
 */
public class InvoiceStatementTransactionPaySearchDto {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date fromDate;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date toDate;
    
    private String maidName;

    public InvoiceStatementTransactionPaySearchDto() {
    }

    public InvoiceStatementTransactionPaySearchDto(Date fromDate, Date toDate, String maidName) {
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.maidName = maidName;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public String getMaidName() {
        return maidName;
    }

    public void setMaidName(String maidName) {
        this.maidName = maidName;
    }
}
