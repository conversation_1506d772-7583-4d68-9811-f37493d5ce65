package com.magnamedia.entity.dto;

import com.magnamedia.entity.Expense;
import com.magnamedia.entity.Supplier;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ExpenseRequestForMaidByGPTDTO {
    private String paymentMethod;
    private String relatedTo;
    private String attachment;
    private Double amount;
    private String amountCurrency;
    private String notes;
    private Supplier supplier;
    private ExpenseBeneficiaryType beneficiaryType;
    private Long beneficiaryId;

    //helper fields
    private Expense expense;
}