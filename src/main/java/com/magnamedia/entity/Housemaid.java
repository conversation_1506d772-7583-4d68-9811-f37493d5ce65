package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntityWithAdditionalInfo;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.maidArrival.MaidArrivalToDo;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import com.magnamedia.entity.serializer.MaidArrivalSerializer;
import com.magnamedia.extra.Gender;
import com.magnamedia.extra.HousemaidReligion;
import com.magnamedia.extra.TerminationMode;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.mastersearch.Searchable;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.HousemaidLiveplace;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.PendingStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.HousemaidDocumentRepository;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.ReplacementRepository;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.envers.NotAudited;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Entity
// jira QA-112
@Table(
        indexes = {
                @Index(columnList = "name", unique = false)
        })
@Searchable(showunName = "Housemaids", order = 1, permissionCode = "HousemaidList")
public class Housemaid extends BaseEntityWithAdditionalInfo {

    @Transient
    public static final Set<HousemaidStatus> rejectedStatuses = new HashSet<>(
            Arrays.asList(HousemaidStatus.EMPLOYEMENT_TERMINATED, HousemaidStatus.WAITING_TO_FILE_ABSCONDING,
                    HousemaidStatus.VISA_UNSUCCESSFUL, HousemaidStatus.REJECTED, HousemaidStatus.UNREACHABLE,
                    HousemaidStatus.UNREACHABLE_AFTER_EXIT, HousemaidStatus.PASSED_EXIT));


    //Jirra ACC-737
    @Transient
    public static final Set<HousemaidStatus> inOfficeStatus
            = new HashSet<>(
            Arrays.asList(
                    HousemaidStatus.AVAILABLE,
                    HousemaidStatus.PENDING_FOR_DISCIPLINE
            ));

    @Column
    @JsonIgnore
    private Date replacementSalaryStartDate;

    @Column
    private String firstName;

    @Column
    private String middleName;

    @Column
    private String lastName;

    @Column
    @Label
    private String name;


    // ACC-1862
    @Column(columnDefinition = "int default 0")
    private Integer workPaidVacationDays;

    @Column
    private Date visaCancellationDate;

    //Jirra ACC-449
    @NotAudited
    @Column(name = "BIRTHDATE")
    @Temporal(TemporalType.DATE)
    private java.util.Date birthdate;

    @Column
    private Boolean excludedFromPayroll = false;

    @Column
    private String insuranceNumber;

    @Column
    private java.sql.Date dateOfInsuranceEndorsement;

    // @Transient
    // private WorkingDaysDetails workingDetails;

    @Column
    private java.util.Date passportExpirydate;

    @NotAudited
    @Column
    private Double foodAllowance;

    @NotAudited
    @Column
    private Double housingAllowance;

    @Column
    private String phoneNumber;

    @Enumerated(EnumType.STRING)
    @Transient
    private HousemaidStatus oldStatus;

    @Enumerated(EnumType.STRING)
    @Column
    private PendingStatus pendingStatus;
    
    //Jirra MW-713
    @Column private String urlToken;

    //Jirra ACC-832
    public Timestamp getInsuranceCancellationDate() {
        NewRequest newRequest = this.getVisaNewRequest();
        CancelRequest cancelRequest = newRequest == null ? null : newRequest.getCancelRequest();
        return cancelRequest == null ? null : cancelRequest.findTaskMoveOutDate("Cancel Insurance");
    }

    //Jirra ACC-608
    @Enumerated(EnumType.STRING)
    @Column
    private HousemaidType housemaidType;

    //Jirra ACC-1093
    @Column
    private Double additionToBalanceDeductionLimit;

    //Jirra SMM-1405
    @NotAudited
    @Column
    private String normalizedPhoneNumber;

    //From Firas
    @NotAudited
    @Column
    private Date availableSince;

    @NotAudited
    @Column
    private Date availableCheckDate;

    // ACC-1793
    @Column(columnDefinition = "boolean default false")
    private Boolean isAfricanCandidate = false;

    //ACC-1515
    @Column(columnDefinition = "boolean default false")
    private Boolean isMaidsAt = false;
    
    //SMM-2693
    @Column
    private Date covidFirstVaccineDoseDate;

    //SMM-2693
    @Column
    private Date covidSecondVaccineDoseDate;
    
    public Double getFoodAllowance() {
        return foodAllowance;
    }

    public void setFoodAllowance(Double foodAllowance) {
        this.foodAllowance = foodAllowance;
    }

    public Double getHousingAllowance() {
        return housingAllowance;
    }

    public void setHousingAllowance(Double housingAllowance) {
        this.housingAllowance = housingAllowance;
    }

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private HousemaidStatus status;

    @Column
    private Date startDate;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotAudited
    @JsonSerialize(using = MaidArrivalSerializer.class)
    private MaidArrivalToDo maidArrivalToDo;

    @Column(columnDefinition = "boolean default false")
    private Boolean liveOut = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean convertMvToCCFromProfile = false; // PAY-1596

    public Timestamp getLandedInDubaiDate() {
        return landedInDubaiDate;
    }

    public void setLandedInDubaiDate(Timestamp landedInDubaiDate) {
        this.landedInDubaiDate = landedInDubaiDate;
    }

    public Double getPreviousStateBasicSalary() {
        return previousStateBasicSalary;
    }

    public void setPreviousStateBasicSalary(Double previousStateBasicSalary) {
        this.previousStateBasicSalary = previousStateBasicSalary;
    }

    private Timestamp landedInDubaiDate;

    @Column(columnDefinition = "double default 0")
    private Double primarySalary = 0.0;

    @Column(columnDefinition = "boolean default false")
    private Boolean travelAssist = false;

    @Column(columnDefinition = "double default 0")
    private Double monthlyLoan = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double overTime = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double holiday = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double airfareFee = 0.0;

    // @Column
    // private Double basicSalary;
//	@Formula("IFNULL(AIRFARE_FEE, 0) + IFNULL(HOLIDAY, 0)+ IFNULL(OVER_TIME, 0) + IFNULL(MONTHLY_LOAN,0) + IFNULL(PRIMARY_SALARY, 0)")
    @Column(columnDefinition = "double default 0")
    private Double basicSalary;

    @Column
    private Double oldRenewalBasicSalary;

    @Column(columnDefinition = "boolean default false")
    private Boolean isBeingPaid50PercentSalary = false;

    @Column
    @ColumnDefault("0")
    private boolean wantToChangeClient;

    @NotAudited
    @Enumerated(EnumType.STRING)
    @Column
    private TerminationMode modeOfTermination;

    public Boolean getIsBeingPaid50PercentSalary() {
        if (isBeingPaid50PercentSalary == null) {
            return false;
        }
        return isBeingPaid50PercentSalary;
    }

    public void setIsBeingPaid50PercentSalary(Boolean isBeingPaid50PercentSalary) {
        this.isBeingPaid50PercentSalary = isBeingPaid50PercentSalary;
    }

    // @Column
    // private Double basicSalary;
    // @Column
    // private Double lostLoans;
    @Enumerated(EnumType.STRING)
    private HousemaidLiveplace living;

    @Column
    private Double defaulMonthlyRepayment;

    @Column
    private String agentId;

    @Column
    private String employeeAccountWithAgent;

    @Column
    private String ansariUniqueId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem manager;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem payrollType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FreedomOperator freedomOperator;

    @Enumerated(EnumType.STRING)
    private HousemaidReligion religion;

    @Column(insertable = false, updatable = false)
    @JsonIgnore
    private String facebookAcc;

    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem nationality;

    @ManyToOne(fetch = FetchType.LAZY)
    @NotAudited
    @JsonSerialize(using = IdJsonSerializer.class)
    private NewRequest visaNewRequest;

    @Column(insertable = false, updatable = false)
    private Timestamp rejectDate;

    @Column
    private boolean isAgency = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    PicklistItem freedomSource;

    @Column
    private Boolean freedomMaid = false;

    @Column
    private Boolean cleanExitMaid = Boolean.FALSE;

    @Column(columnDefinition = "tinyint(1) default 0")
    private Boolean notArabicSpeaker;

    @NotAudited
    @Column
    private Date pendingSince;

    @NotAudited
    @Column
    private Date pendingUntil;

    @Column
    private String passportNumber;

    //Jirra ACC-737
    @Transient
    private String initialedName;

    public String getPassportNumber() {
        return passportNumber;
    }

    public void setPassportNumber(String passportNumber) {
        this.passportNumber = passportNumber;
    }

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem reasonOfPending;

    //Jirra ACC-837
    @Transient
    private String eid;

    @Transient
    private Attachment forntEid;

    @Column(columnDefinition = "bigint(20) default 0")
    private Long cashAdvanceCounter = 0L;

    //Jirra VPM-1624
    @Column(columnDefinition = "boolean default false")
    private Boolean isFailedMedical = false;

    //Jirra SMM-2440
    @NotAudited
    @Column(length = 30)
    private String whatsAppPhoneNumber;

    //Jirra SMM-2440
    @NotAudited
    @Column(length = 30)
    private String normalizedWhatsAppPhoneNumber;
    
    //Jirra ACC-2802
    @NotAudited
    @Column
    private Date dateOfTermination;
    
    //Jirra ACC-2802
    @Column
    private Date finalSettlementCalculationDate;
    //Jirra ACC-2445
    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem yayaLanguage;

    @Enumerated
    @Column
    private Gender gender;

    @ManyToOne(fetch = FetchType.LAZY)
    private PicklistItem location;

    public Timestamp getRejectDate() {
        return rejectDate;
    }

    public void setRejectDate(Timestamp rejectDate) {
        this.rejectDate = rejectDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private void updateName() {
        this.name = "";
        if (this.firstName != null && !this.firstName.isEmpty()) {
            name += firstName + " ";
        }
        if (this.middleName != null && !this.middleName.isEmpty()) {
            name += middleName + " ";
        }
        if (this.lastName != null && !this.lastName.isEmpty()) {
            name += lastName + " ";
        }
        if (this.name != null) {
            this.name = this.name.trim();
        }

    }

    public String getFirstName() {
        return firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
        updateName();
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
        updateName();
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
        updateName();
    }

    public Date getPassportExpirydate() {
        return passportExpirydate;
    }

    public void setPassportExpirydate(Date passportExpirydate) {
        this.passportExpirydate = passportExpirydate;
    }

    public Date getVisaCancellationDate() {
        return visaCancellationDate;
    }

    public void setVisaCancellationDate(Date visaCancellationDate) {
        this.visaCancellationDate = visaCancellationDate;
    }

    public HousemaidStatus getStatus() {
        return status;
    }

    public void setStatus(HousemaidStatus status) {
        this.status = status;
        this.oldStatus = this.status;

        if (oldStatus != null
                && status != null
                && oldStatus.equals(HousemaidStatus.PENDING_FOR_DISCIPLINE)
                && pendingStatus != null
                && !oldStatus.equals(status)) {

            pendingStatus = null;
        }
    }

    public Boolean getTravelAssist() { return travelAssist; }

    public void setTravelAssist(Boolean travelAssist) { this.travelAssist = travelAssist; }

    public Integer getWorkPaidVacationDays() {
        return workPaidVacationDays;
    }

    public void setWorkPaidVacationDays(Integer workPaidVacationDays) {
        this.workPaidVacationDays = workPaidVacationDays;
    }

    public PendingStatus getPendingStatus() {
        return pendingStatus;
    }

    public void setPendingStatus(PendingStatus pendingStatus) {
        this.pendingStatus = pendingStatus;
    }

    public Date getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(Date birthdate) {
        this.birthdate = birthdate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Double getBasicSalary() {
        return basicSalary;
    }

    public void setBasicSalary(Double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public Double getOldRenewalBasicSalary() {
        return oldRenewalBasicSalary;
    }

    public void setOldRenewalBasicSalary(Double oldRenewalBasicSalary) {
        this.oldRenewalBasicSalary = oldRenewalBasicSalary;
    }

    public HousemaidLiveplace getLiving() {
        return living;
    }

    public void setLiving(HousemaidLiveplace living) {
        this.living = living;
    }

    // public Double getLostLoans() {
    // return lostLoans;
    // }
    //
    // public void setLostLoans(Double lostLoans) {
    // this.lostLoans = lostLoans;
    // }
    public Double getDefaulMonthlyRepayment() {
        return defaulMonthlyRepayment;
    }

    public void setDefaulMonthlyRepayment(Double defaulMonthlyRepayment) {
        this.defaulMonthlyRepayment = defaulMonthlyRepayment;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getEmployeeAccountWithAgent() {
        return employeeAccountWithAgent;
    }

    public void setEmployeeAccountWithAgent(String employeeAccountWithAgent) {
        this.employeeAccountWithAgent = employeeAccountWithAgent;
    }

    public String getAnsariUniqueId() {
        return ansariUniqueId;
    }

    public void setAnsariUniqueId(String ansariUniqueId) {
        this.ansariUniqueId = ansariUniqueId;
    }

    public HousemaidReligion getReligion() {
        return religion;
    }

    public void setReligion(HousemaidReligion religion) {
        this.religion = religion;
    }

    public String getFacebookAcc() {
        return facebookAcc;
    }

    public void setFacebookAcc(String facebookAcc) {
        this.facebookAcc = facebookAcc;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    public NewRequest getVisaNewRequest() {
        return visaNewRequest;
    }

    public void setVisaNewRequest(NewRequest visaNewRequest) {
        this.visaNewRequest = visaNewRequest;
    }

    public boolean isIsAgency() {
        return isAgency;
    }

    public void setIsAgency(boolean isAgency) {
        this.isAgency = isAgency;
    }

    public PicklistItem getFreedomSource() {
        return freedomSource;
    }

    public void setFreedomSource(PicklistItem freedomSource) {
        this.freedomSource = freedomSource;
    }

    public Boolean getFreedomMaid() {
        return freedomMaid;
    }

    public void setFreedomMaid(Boolean freedomMaid) {
        this.freedomMaid = freedomMaid;
    }

    public Boolean getCleanExitMaid() {
        return cleanExitMaid;
    }

    public void setCleanExitMaid(Boolean cleanExitMaid) {
        this.cleanExitMaid = cleanExitMaid;
    }

    public Double getPrimarySalary() {
        return primarySalary;
    }

    public void setPrimarySalary(Double primarySalary) {
        this.primarySalary = primarySalary;
    }

    public Double getMonthlyLoan() {
        return monthlyLoan;
    }

    public void setMonthlyLoan(Double monthlyLoan) {
        this.monthlyLoan = monthlyLoan;
    }

    public Double getOverTime() {
        return overTime;
    }

    public void setOverTime(Double overTime) {
        this.overTime = overTime;
    }

    public Double getHoliday() {
        return holiday;
    }

    public void setHoliday(Double holiday) {
        this.holiday = holiday;
    }

    public Double getAirfareFee() {
        return airfareFee;
    }

    public void setAirfareFee(Double airfareFee) {
        this.airfareFee = airfareFee;
    }

    public FreedomOperator getFreedomOperator() {
        return freedomOperator;
    }

    public void setFreedomOperator(FreedomOperator freedomOperator) {
        this.freedomOperator = freedomOperator;
    }

    public Boolean getExcludedFromPayroll() {
        return excludedFromPayroll;
    }

    public void setExcludedFromPayroll(Boolean excludedFromPayroll) {
        this.excludedFromPayroll = excludedFromPayroll;
    }

    public PicklistItem getManager() {
        return manager;
    }

    public void setManager(PicklistItem manager) {
        this.manager = manager;
    }

    public PicklistItem getPayrollType() {
        return payrollType;
    }

    public void setPayrollType(PicklistItem payrollType) {
        this.payrollType = payrollType;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public java.sql.Date getDateOfInsuranceEndorsement() {
        return dateOfInsuranceEndorsement;
    }

    public void setDateOfInsuranceEndorsement(java.sql.Date dateOfInsuranceEndorsement) {
        this.dateOfInsuranceEndorsement = dateOfInsuranceEndorsement;
    }

    @Transient
    private Double previousStateBasicSalary;

//	@Transient
//	private boolean salaryUpdatedFromJob = false;

    public String getHousemaidStringSource() {
        if (isIsAgency())
            return "Agency";
        if (getFreedomMaid()) {
            String source = "UNKNOWN";
            if (getFreedomOperator() != null)
                source = getFreedomOperator().getName();
            return "Freedom: " + source;
        }

        return "Clean Exit";
    }

    @PostLoad
    public void setPreviousState() {
        previousStateBasicSalary = this.getBasicSalary();
        // copy fields
    }

    public Boolean getNotArabicSpeaker() {
        return notArabicSpeaker;
    }

    public void setNotArabicSpeaker(Boolean notArabicSpeaker) {
        this.notArabicSpeaker = notArabicSpeaker;
    }

    @JsonIgnore
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public HousemaidType getHousemaidType() {
        return housemaidType;
    }

    public void setHousemaidType(HousemaidType housemaidType) {
        this.housemaidType = housemaidType;
    }

    public Double getAdditionToBalanceDeductionLimit() {
        return additionToBalanceDeductionLimit;
    }

    public void setAdditionToBalanceDeductionLimit(Double additionToBalanceDeductionLimit) {
        this.additionToBalanceDeductionLimit = additionToBalanceDeductionLimit;
    }

    @JsonIgnore
    public String getNormalizedPhoneNumber() {
        return normalizedPhoneNumber;
    }

    public void setNormalizedPhoneNumber(String normalizedPhoneNumber) {
        this.normalizedPhoneNumber = normalizedPhoneNumber;
    }

    public Date getAvailableSince() {
        return availableSince;
    }

    public void setAvailableSince(Date availableSince) {
        this.availableSince = availableSince;
    }

    public Boolean getIsMaidsAt() {
        return isMaidsAt != null && isMaidsAt;
    }

    public void setIsMaidsAt(Boolean isMaidsAt) {
        this.isMaidsAt = isMaidsAt;
    }
    
    public Date getCovidFirstVaccineDoseDate() {
        return covidFirstVaccineDoseDate;
    }

    public void setCovidFirstVaccineDoseDate(Date covidFirstVaccineDoseDate) {
        this.covidFirstVaccineDoseDate = covidFirstVaccineDoseDate;
    }

    public Date getCovidSecondVaccineDoseDate() {
        return covidSecondVaccineDoseDate;
    }

    public void setCovidSecondVaccineDoseDate(Date covidSecondVaccineDoseDate) {
        this.covidSecondVaccineDoseDate = covidSecondVaccineDoseDate;
    }
    
    public Boolean getIsAfricanCandidate() {
        return isAfricanCandidate;
    }

    public void setIsAfricanCandidate(Boolean isAfricanCandidate) {
        this.isAfricanCandidate = isAfricanCandidate;
    }


    public Date getAvailableCheckDate() {
        if (availableCheckDate == null) {
            return getAvailableSince();
        }
        return availableCheckDate;
    }

    public void setAvailableCheckDate(Date availableCheckDate) {
        this.availableCheckDate = availableCheckDate;
    }

    public Client getCurrentClient() {
        Predicate<Contract> byContractStatus = contract -> contract.getStatus() == ContractStatus.ACTIVE;
        List<Contract> activeContracts = Setup.getRepository(ContractRepository.class).findByHousemaid(this).stream()
                .filter(byContractStatus).collect(Collectors.<Contract>toList());

        if (activeContracts.size() > 0) {
            return activeContracts.get(0).getClient();
        } else {
            return null;
        }
    }

    public Date getPendingSince() {
        return pendingSince;
    }

    public void setPendingSince(Date pendingSince) {
        this.pendingSince = pendingSince;
    }

    public Date getPendingUntil() {
        return pendingUntil;
    }

    public void setPendingUntil(Date pendingUntil) {
        this.pendingUntil = pendingUntil;
    }

    public PicklistItem getReasonOfPending() {
        return reasonOfPending;
    }

    public void setReasonOfPending(PicklistItem reasonOfPending) {
        this.reasonOfPending = reasonOfPending;
    }

    public String getEid() {
        if (this.visaNewRequest != null)
            return this.visaNewRequest.getEidApplicationNumber();
        return eid;
    }

    public Attachment getForntEid() {
        HousemaidDocumentRepository documentRepository =
                Setup.getRepository(HousemaidDocumentRepository.class);
        PicklistItem docType = PicklistHelper.getItem("HousemaidDocType", "eid_front_side");
        List<HousemaidDocument> docs = documentRepository.findByHousemaidAndType(this, docType);
        if (docs != null && !docs.isEmpty())
            return docs.get(0).getAttachment("eid_front_side");
        return null;
    }

    //Jirra ACC-737
    public boolean isInOffice() {
        if (this.getStatus() == null) {
            return false;
        } else {
            return inOfficeStatus.contains(this.getStatus());
        }
    }

    public String getInitaledName() {

        if (name == null) {
            return "";
        }
        if (name.equals("")) {
            return "";
        }
        if (initialedName == null) {

            String[] splits = this.name.split(" ");
            if (splits.length > 0) {
                initialedName = ""
                        + splits[0];
                for (int i = 1; i < splits.length; i++) {
                    initialedName += " " + splits[i].substring(0,
                            1)
                            .toUpperCase() + ".";
                }
            }

        }
        return initialedName;
    }

    public Integer getReplacementsCount() {
        List<Replacement> reps = Setup.getRepository(ReplacementRepository.class).findByOldHousemaid(this);
        return reps.size();
    }

    public Integer getFaultyReplacementsCount() {
        List<Replacement> reps = Setup.getRepository(ReplacementRepository.class).findFaultyReplacementsByOldHousemaid(this);
        return reps.size();
    }

    @BeforeUpdate
    private void preSave() {
        if ((this.basicSalary != null)
                && !this.basicSalary.equals(previousStateBasicSalary)) {
            throw new RuntimeException("Total Salary is not updatable");
        }
        List<HousemaidStatus> statuses = Arrays.asList(HousemaidStatus.EMPLOYEMENT_TERMINATED,
                HousemaidStatus.UNREACHABLE_AFTER_EXIT, HousemaidStatus.REJECTED, HousemaidStatus.PASSED_EXIT);
        Housemaid old = Setup.getApplicationContext().getBean(HousemaidRepository.class).getOne(this.getId());
        if (old.getStatus() != this.status && statuses.contains(this.status)) {
            this.visaCancellationDate = new LocalDate().toDate();
        }
        // recalculate basic salary
        this.basicSalary = caculateBasicSalary();
        this.previousStateBasicSalary = this.basicSalary;
    }

    public double caculateBasicSalary() {
        return (airfareFee == null ? 0 : airfareFee) + (holiday == null ? 0 : holiday)
                + (overTime == null ? 0 : overTime) + (monthlyLoan == null ? 0 : monthlyLoan)
                + (primarySalary == null ? 0 : primarySalary);
    }

    public void setCashAdvanceCounter(Long cashAdvanceCounter) {
        this.cashAdvanceCounter = cashAdvanceCounter;
    }

    public Long getCashAdvanceCounter() {
        return cashAdvanceCounter;
    }

    public Boolean getFailedMedical() {
        return isFailedMedical;
    }

    public void setFailedMedical(Boolean failedMedical) {
        isFailedMedical = failedMedical;
    }

    public Date getDateOfTermination() {
        return dateOfTermination;
    }

    public void setDateOfTermination(Date dateOfTermination) {
        this.dateOfTermination = dateOfTermination;
    }
    
    public PicklistItem getYayaLanguage() {
        return yayaLanguage;
    }

    public Date getFinalSettlementCalculationDate() {
        return finalSettlementCalculationDate;
    }

    public void setFinalSettlementCalculationDate(Date finalSettlementCalculationDate) {
        this.finalSettlementCalculationDate = finalSettlementCalculationDate;
    }
    
    public void setYayaLanguage(PicklistItem yayaLanguage) {
        this.yayaLanguage = yayaLanguage;
    }
    
    @JsonIgnore
    public String getYayaAppNotificationLang(){
        
        if (this.getYayaLanguage() != null){
            return this.getYayaLanguage().getCode();
        }
        return Setup.getItem("template_languages", "en").getCode();
    }
    
    public Date getReplacementSalaryStartDate() {
        return replacementSalaryStartDate;
    }

    public void setReplacementSalaryStartDate(Date replacementSalaryStartDate) {
        this.replacementSalaryStartDate = replacementSalaryStartDate;
    }

    public MaidArrivalToDo getMaidArrivalToDo() {
        return maidArrivalToDo;
    }

    public void setMaidArrivalToDo(MaidArrivalToDo maidArrivalToDo) {
        this.maidArrivalToDo = maidArrivalToDo;
    }

    @JsonIgnore
    public String getWhatsAppPhoneNumber() {
        return whatsAppPhoneNumber;
    }

    public void setWhatsAppPhoneNumber(String whatsAppPhoneNumber) {
        this.whatsAppPhoneNumber = whatsAppPhoneNumber;
    }

    @JsonIgnore
    public String getNormalizedWhatsAppPhoneNumber() {
        return normalizedWhatsAppPhoneNumber;
    }

    public void setNormalizedWhatsAppPhoneNumber(String normalizedWhatsAppPhoneNumber) {
        this.normalizedWhatsAppPhoneNumber = normalizedWhatsAppPhoneNumber;
    }

    public String getUrlToken() {
        return urlToken;
    }

    public void setUrlToken(String urlToken) {
        this.urlToken = urlToken;
    }

    public boolean isWantToChangeClient() {
        return wantToChangeClient;
    }

    public void setWantToChangeClient(boolean wantToChangeClient) {
        this.wantToChangeClient = wantToChangeClient;
    }

    public TerminationMode getModeOfTermination() {
        return modeOfTermination;
    }

    public Gender getGender() { return gender == null ? Gender.Female : gender; }

    public void setGender(Gender gender) { this.gender = gender; }

    public void setModeOfTermination(TerminationMode modeOfTermination) {
        this.modeOfTermination = modeOfTermination;
    }

    public PicklistItem getLocation() {
        return location;
    }

    public void setLocation(PicklistItem location) {
        this.location = location;
    }

    public Boolean getLiveOut() {
        return liveOut;
    }

    public void setLiveOut(Boolean liveOut) {
        this.liveOut = liveOut;
    }

    public Boolean getConvertMvToCCFromProfile() {
        return convertMvToCCFromProfile != null && convertMvToCCFromProfile;
    }

    public void setConvertMvToCCFromProfile(Boolean convertMvToCCFromProfile) {
        this.convertMvToCCFromProfile = convertMvToCCFromProfile;
    }
}