package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.InvoiceStatementTransactionType;

import javax.persistence.*;
import java.util.Date;


@Entity
public class InvoiceStatementTransaction extends BaseEntity {


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private InvoiceStatement statement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private Transaction transaction;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Enumerated(EnumType.STRING)
    private InvoiceStatementTransactionType type = InvoiceStatementTransactionType.PENDING_MATCHED;

    @Column
    private Double amount = 0.0;

    @Column
    private String reason;

    @Column
    private Date visitDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ExpenseRequestTodo expenseRequestTodo;

    @Column(columnDefinition = "boolean default false")
    private Boolean confirmed = false;

    @Column
    private String relatedMaidName;

    @Column String passportNo;


    public InvoiceStatement getStatement() {
        return statement;
    }

    public void setStatement(InvoiceStatement statement) {
        this.statement = statement;
    }

    public ExpenseRequestTodo getExpenseRequestTodo() {
        return expenseRequestTodo;
    }

    public void setExpenseRequestTodo(ExpenseRequestTodo expenseRequestTodo) {
        this.expenseRequestTodo = expenseRequestTodo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getVisitDate() {
        return visitDate;
    }

    public void setVisitDate(Date visitDate) {
        this.visitDate = visitDate;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }


    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public InvoiceStatementTransactionType getType() {
        return type;
    }

    public void setType(InvoiceStatementTransactionType type) {
        this.type = type;
    }

    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public String getRelatedMaidName() {
        return relatedMaidName;
    }

    public void setRelatedMaidName(String relatedMaidName) {
        this.relatedMaidName = relatedMaidName;
    }

    public String getPassportNo() {
        return passportNo;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

}
