package com.magnamedia.entity.RiskDocumentsManagement;

import com.magnamedia.core.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.AuditOverride;

import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@AuditOverride(isAudited = false)
public class RiskDocumentsManagementVisitHistory extends BaseEntity {

    @Column
    private String serviceReportName;

    @Column
    private Integer frequencyOfVisit;

    @Column
    @Enumerated(EnumType.STRING)
    private RiskDocumentsManagement.FrequencyType frequencyType;

    @Column
    private Date dateOfVisit;

    @Column
    private String attachmentUUID;

    @ManyToOne(fetch = FetchType.LAZY)
    private RiskDocumentsManagement riskDocumentsManagement;
}