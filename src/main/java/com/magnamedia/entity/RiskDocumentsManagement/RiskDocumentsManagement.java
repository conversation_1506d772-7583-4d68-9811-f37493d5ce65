package com.magnamedia.entity.RiskDocumentsManagement;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.extra.LabelValueEnum;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

@Entity
@Getter
@Setter
public class RiskDocumentsManagement extends BaseEntity {
    private static final Logger logger = Logger.getLogger(RiskDocumentsManagement.class.getName());

    public enum FrequencyType {
        WEEKLY,
        MONTHLY
    }

    public enum Status {
        ACTIVE,
        DISABLED,
        EXPIRED,
        UNDER_RENEWAL
    }

    public enum DocumentRelatedTo implements LabelValueEnum {

        CENTER("Center"),
        ACCOMMODATION("Accommodation"),
        KIOSK("Kiosk");

        private final String label;

        DocumentRelatedTo(String label) {
            this.label = label;
        }

        @Override
        public String getLabel() {
            return this.label;
        }
    }

    @Column(nullable = false)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem governmentEntity;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem type;

    @Column
    private String risk;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem importance;

    @Column
    private Date expiryDate;

    @ElementCollection(fetch = FetchType.LAZY)
    @NotAudited
    @Enumerated(EnumType.STRING)
    private List<DocumentRelatedTo> relatesTo = new ArrayList<>();

    @Column
    private Date issuanceDate;

    @Column
    private Date registrationDate;

    @Column
    private Date insuranceExpirationDate;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RiskDocumentsManagementLayer> layers = new ArrayList<>();

    @Column(columnDefinition = "boolean default false")
    private boolean requiresRenewal = false;

    @Column(columnDefinition = "boolean default false")
    private boolean requiresVisit = false;

    @Column
    @Enumerated(EnumType.STRING)
    private Status status;

    @Column(columnDefinition = "boolean default true")
    private boolean active = true;

    @Column
    private String serviceReportName;

    @Column
    private Integer frequencyOfVisit;

    @Column
    @Enumerated(EnumType.STRING)
    private FrequencyType frequencyType;

    @Column
    private Date lastVisitDate;

    @Column
    private Date nextVisitDate;

    @JsonSerialize(using = IdLabelListSerializer.class)
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "RISK_DOCUMENTS_MANAGEMENT_ACCOUNTABLE_PERSON",
            joinColumns = @JoinColumn(
                    name = "DOCUMENT_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "ROLE_ID",
                    referencedColumnName = "ID"))
    private List<RiskDocumentsManagementRole> accountablePerson = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RiskDocumentsManagementHistory> documentHistories = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RiskDocumentsManagementVisitHistory> documentVisitHistories = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RiskDocumentsManagementTodo> riskManagementTodos = new ArrayList<>();
}