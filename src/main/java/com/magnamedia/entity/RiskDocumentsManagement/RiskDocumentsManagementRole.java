package com.magnamedia.entity.RiskDocumentsManagement;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
public class RiskDocumentsManagementRole extends BaseEntity {

    @Column
    @ColumnDefault("false")
    private boolean isDeleted = false;

    @Column
    @Label
    private String name;

    @JsonSerialize(using = IdLabelListSerializer.class)
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "RISK_DOCUMENTS_MANAGEMENT_ROLE_RESPONSIBLE_USER",
            joinColumns = @JoinColumn(
                    name = "Risk_Documents_Management_Role_ID",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "USER_ID",
                    referencedColumnName = "ID"))
    private List<User> responsibleUser = new ArrayList<>();
}