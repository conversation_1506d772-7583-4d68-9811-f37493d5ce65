package com.magnamedia.entity.RiskDocumentsManagement;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.extra.LabelValueEnum;
import lombok.Getter;
import lombok.Setter;
import net.minidev.json.annotate.JsonIgnore;

import javax.persistence.*;
import java.sql.Date;
import java.util.logging.Logger;

@Entity
@Getter
@Setter
@Table
public class RiskDocumentsManagementTodo extends BaseEntity {
    private static final Logger logger = Logger.getLogger(RiskDocumentsManagementTodo.class.getName());

    public enum Status implements LabelValueEnum {
        PENDING_RENEWAL("Pending Renewal"),
        UNDER_RENEWAL("Under Renewal"),
        DONE_RENEWAL("Done Renewal"),

        RTA_TESTING("RTA Testing"),
        DONE_RTA_TESTING("Done RTA Testing"),
        PENDING_INSURANCE("Pending Insurance"),
        DONE_INSURANCE("Done Insurance"),
        PENDING_REGISTRATION("Pending Registration"),
        DONE_REGISTRATION("Done Registration"),

        PENDING_VISIT("Pending Visit"),
        VISIT_DONE("Visit Done"),

        CLOSED("Closed");

        Status(String label) {
            this.label = label;
        }

        private final String label;

        @Override
        public String getLabel() {
            return label;
        }
    }

    public enum Type {
        VEHICLE,
        NON_VEHICLE,
        VISIT
    }

    @Column
    @Enumerated(EnumType.STRING)
    private Status status;

    @Column
    @Enumerated(EnumType.STRING)
    private Type type;

    @ManyToOne(fetch = FetchType.LAZY)
    private RiskDocumentsManagement riskDocumentsManagement;

    @Column
    private String attachmentUUID;

    // Vehicle Type
    @Column
    private Date newVehicleExpirationDate;

    @Column
    private Date registrationDate;

    @Column
    private Date insuranceExpirationDate;

    @Column
    private Date issuanceDate;

    // Non Vehicle Type
    @Column
    private Date newIssuanceDate;

    @Column
    private Date newExpiryDate;

    // Visit Type
    @Column
    private Date visitDate;

    @Column
    private Date newVisitDate;

    @Column
    private Date dueDateForNextVisit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private RiskDocumentsManagementLayer riskDocumentsManagementLayer;

    @Lob
    private String rejectionNotes;

    @OneToOne(fetch = FetchType.LAZY)
    private User assignee;
}