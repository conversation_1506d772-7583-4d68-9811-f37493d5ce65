package com.magnamedia.entity.payroll.logging;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;

import javax.persistence.*;
import java.sql.Date;

@Entity
public class HousemaidPayrollLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PayrollAccountantTodo payrollAccountantTodo;

    private Date payrollMonth;

    public PayrollAccountantTodo getPayrollAccountantTodo() {
        return payrollAccountantTodo;
    }

    public void setPayrollAccountantTodo(PayrollAccountantTodo payrollAccountantTodo) {
        this.payrollAccountantTodo = payrollAccountantTodo;
    }

    public Date getPayrollMonth() {
        return payrollMonth;
    }

    public void setPayrollMonth(Date payrollMonth) {
        this.payrollMonth = payrollMonth;
    }
} 