package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

public enum PayrollTransferUnmatchedReason implements LabelValueEnum {
    PNL_CODE_IS_MISSING("PNL Code is Missing"),
    ACCOUNTANT_TODO_IS_OPEN("A Matching is Found, but Accountant To-Do is still open"),
    NO_SALARY_PAYMENT_RECORD("No salary payment record");

    private final String label;

    PayrollTransferUnmatchedReason(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() { return label; }
}