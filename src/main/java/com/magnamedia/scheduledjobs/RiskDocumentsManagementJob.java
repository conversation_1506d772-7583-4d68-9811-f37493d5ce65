package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagement;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementLayer;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementRole;
import com.magnamedia.entity.RiskDocumentsManagement.RiskDocumentsManagementTodo;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementLayerRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementRepository;
import com.magnamedia.repository.RiskDocumentsManagement.RiskDocumentsManagementToDoRepository;
import com.magnamedia.service.MessagingService;
import com.magnamedia.service.RiskDocumentsManagement.RiskDocumentsManagementRoleService;
import com.magnamedia.service.RiskDocumentsManagement.RiskDocumentsManagementToDoService;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class RiskDocumentsManagementJob implements MagnamediaJob  {

    private static final Logger logger = Logger.getLogger(RiskDocumentsManagementJob.class.getName());

    private RiskDocumentsManagementRepository riskDocumentsManagementRepository;
    private RiskDocumentsManagementToDoService riskDocumentsManagementToDoService;
    private RiskDocumentsManagementLayerRepository riskDocumentsManagementLayerRepository;
    private RiskDocumentsManagementToDoRepository riskDocumentsManagementToDoRepository;
    private MessagingService messagingService;
    @Override
    public void run(Map<String, ?> map) {
        logger.info("Started");
        riskDocumentsManagementRepository = Setup.getRepository(RiskDocumentsManagementRepository.class);
        riskDocumentsManagementLayerRepository = Setup.getRepository(RiskDocumentsManagementLayerRepository.class);
        riskDocumentsManagementToDoRepository = Setup.getRepository(RiskDocumentsManagementToDoRepository.class);

        riskDocumentsManagementToDoService = Setup.getApplicationContext()
                .getBean(RiskDocumentsManagementToDoService.class);

        messagingService = Setup.getApplicationContext().getBean(MessagingService.class);

        createPendingRenewalToDoAndSendEmails();
        createVisitToDoAndSendEmails();
        sendRiskDocumentsManagementLayer2AlertJob();
        sendRiskDocumentsManagementExpiryDateAlert();
        terminateDocument();

        logger.info("Finished");
    }

    private void createPendingRenewalToDoAndSendEmails() {
        Page<Map> p;
        Long lastId = -1L;
        do {
            p = riskDocumentsManagementLayerRepository.findByCreationRenewalToDoDateAndType(
                    lastId,
                    new java.sql.Date(new Date().getTime()),
                    RiskDocumentsManagementLayer.Type.LAYER_ONE,
                    PageRequest.of(0, 200));

            if (p.getContent().isEmpty()) break;

            p.getContent()
                    .forEach(l -> {
                        try {
                            riskDocumentsManagementToDoService.createPendingRenewalToDoAndSendEmail(Long.valueOf(l.get("documentId").toString())
                                    , (RiskDocumentsManagementLayer) l.get("layer"));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });

            if (!p.getContent().isEmpty()) {
                lastId = ((RiskDocumentsManagementLayer) p.getContent().get(p.getContent().size() -1).get("layer")).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    private void createVisitToDoAndSendEmails() {
        Page<RiskDocumentsManagement> p;
        Long lastId = -1L;
        do {
            p = riskDocumentsManagementRepository.getRequireVisitsDocumentsByDate(
                    lastId,
                    new java.sql.Date(new Date().getTime()),
                    PageRequest.of(0, 200));

            if (p.getContent().isEmpty()) return;

            p.getContent()
                    .forEach(d -> {
                        try {
                            riskDocumentsManagementToDoService.createVisitTodoAndSendEmail(d);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() -1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    private void sendRiskDocumentsManagementLayer2AlertJob() {
        Page<RiskDocumentsManagementTodo> p;
        Long lastId = -1L;
        do {
            p = riskDocumentsManagementToDoRepository
                    .findByStatusesWithActiveDocumentsAndLayers(lastId,
                            Arrays.asList(RiskDocumentsManagementTodo.Status.UNDER_RENEWAL, RiskDocumentsManagementTodo.Status.PENDING_RENEWAL),
                            new java.sql.Date(new Date().getTime()), PageRequest.of(0, 200));

            logger.info("layer 2 Todos size: " + p.getContent().size());

            if (p.getContent().isEmpty()) return;

            p.getContent().forEach(todo -> {
                try {
                    logger.info("todo id: " + todo.getId());
                    RiskDocumentsManagement document = todo.getRiskDocumentsManagement();

                    Map<String, String> parameters = new HashMap<>();
                    parameters.put("document_name", document.getName());
                    parameters.put("expiry_date", new LocalDate(document.getExpiryDate()).toString("yyyy-MM-dd"));
                    parameters.put("layer2_days_before_expiry", String.valueOf(todo.getRiskDocumentsManagementLayer().getDaysBeforeExpiry()));
                    parameters.put("assignee", todo.getAssignee() != null && todo.getAssignee().getFullName() != null ? todo.getAssignee().getFullName() : "");

                    if (todo.getRiskDocumentsManagement().getLayers().isEmpty()) return;

                    logger.info("document layers of type 2 size: " + (todo.getRiskDocumentsManagement().getLayers().size() - 1));

                    todo.getRiskDocumentsManagement().getLayers()
                            .stream().filter(l -> l.getType().equals(RiskDocumentsManagementLayer.Type.LAYER_TWO))
                            .forEach(l ->
                            messagingService.sendEmailToOfficeStaff(
                                    todo.getStatus().equals(RiskDocumentsManagementTodo.Status.UNDER_RENEWAL) ?
                                            "risk_document_layer_two_under_renewal_alert2" :
                                            "risk_document_layer_two_pending_renewal_alert",
                                    parameters,
                                    RiskDocumentsManagementRoleService.getEmailsFromUsers(
                                            l.getRoleAssignee()),
                                    document.getName() + " is expiring in " + l.getDaysBeforeExpiry() + " days "));

                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() -1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    private void sendRiskDocumentsManagementExpiryDateAlert() {

        Page<RiskDocumentsManagement> p;
        Long lastId = -1L;

        do {
            p = riskDocumentsManagementRepository
                    .getRequireRenewalsDocumentsByDate(lastId, new java.sql.Date(new Date().getTime()),
                            PageRequest.of(0, 200));

            if (p.getContent().isEmpty()) break;

            p.getContent().forEach(document -> {
                try {
                    List<RiskDocumentsManagementLayer> layers = document.getLayers();

                    List<RiskDocumentsManagementRole> l = layers.stream()
                            .flatMap(r -> r.getRoleAssignee().stream()).collect(Collectors.toList());

                    Map<String, String> parameters = new HashMap<>();
                    parameters.put("document_name", document.getName());
                    parameters.put("status", document.getStatus() != null ? document.getStatus().toString() : "");

                    messagingService.sendEmailToOfficeStaff(
                                    "risk_document_expiry_date_alert",
                                    parameters,
                                    RiskDocumentsManagementRoleService.getEmailsFromUsers(l),
                                    document.getName() + " expires TODAY");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() -1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void terminateDocument() {
        Page<RiskDocumentsManagement> p;
        Long lastId = -1L;
        do {
            p = riskDocumentsManagementRepository
                    .getExpiredDocuments(lastId, new java.sql.Date(new Date().getTime()), PageRequest.of(0, 200));

            if (p.getContent().isEmpty()) break;

            p.getContent()
                    .forEach(d -> {
                        try {
                            d.setStatus(RiskDocumentsManagement.Status.EXPIRED);
                            riskDocumentsManagementRepository.silentSave(d);
                            riskDocumentsManagementToDoService.closeAllOpenedTodosByDocument(d);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() -1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

}
