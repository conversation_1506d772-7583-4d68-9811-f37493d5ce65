package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.helper.PaginationHelper;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.MessagingService;

import java.util.*;
import java.util.logging.Logger;

public class LateDDCancellationNotificationJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(LateDDCancellationNotificationJob.class.getName());

    private ContractRepository contractRepository;
    private MessagingService messagingService;

    public LateDDCancellationNotificationJob() {
        contractRepository = Setup.getRepository(ContractRepository.class);
        messagingService = Setup.getApplicationContext().getBean(MessagingService.class);
    }

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("Started");
        PaginationHelper.processPaginated(
                "LateDDCancellationNotificationJob",
                contractRepository::findContractsWithEligibleDDBsForLateCancellationNotification,
                this::sendMessage
        );
        logger.info("Ended");
    }

    private void sendMessage(Contract contract) {
        logger.info("Contract ID: " + contract);
        String templateName = contract.isMaidCc() ?
                CcNotificationTemplateCode.CC_CLIENTS_LATE_DD_CANCELLATIONS_NOTIFICATION.toString() :
                MvNotificationTemplateCode.MV_CLIENTS_LATE_DD_CANCELLATIONS_NOTIFICATION.toString();

        Map<String, String> map = new HashMap<>();
        map.put("client_name", contract.getClient().getName() != null ? contract.getClient().getName() : "");
        messagingService.sendMessageToClient(
                contract,
                map,
                new HashMap<>(),
                contract.getId(),
                contract.getEntityType(),
                TemplateUtil.getTemplate(templateName));
    }
} 