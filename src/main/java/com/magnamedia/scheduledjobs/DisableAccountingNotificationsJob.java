package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.service.DisableAccountingNotificationService;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.entity.*;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.*;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DisableAccountingNotificationsJob implements MagnamediaJob {
    private final Logger LOGGER = Logger.getLogger(DisableAccountingNotificationsJob.class.toString());

    @Override
    public void run(Map<String, ?> parameters) {
        LOGGER.log(Level.INFO, "DisableNotificationsJob started");

        DisableAccountingNotificationService disableAccountingNotificationService =
            Setup.getApplicationContext().getBean(DisableAccountingNotificationService.class);

        disableAccountingNotificationService.disableNotificationAfterXDay(3);
        disableAccountingNotificationService.disableNotificationAfterXDay(7);

        disableAccountingNotificationService.disableNotificationOnBouncedPaymentReceived();

        disableAccountingNotificationService.disableProofTransferSubmit();

        disableAccountingNotificationService.incompleteDdStatusChanged();

        disableAccountingNotificationService.disableOnDdRejectedAuthorizationConfirmed();

        // ACC-4715 ACC-5214
        disableAccountingNotificationService.disableNotificationReceivedAfterXDay(2);
        disableAccountingNotificationService.disableNotificationAfterSmsSentXDay(2);

        disableAccountingNotificationService.disableOnlineCardPaymentForApprovalOnPaymentStatusChanged();

        // ACC-4905
        disableAccountingNotificationService.disableOnNewDDAdded();

        disableAccountingNotificationService.disableNotificationOnDdsGotCanceled();

        disableAccountingNotificationService.disableNotificationClientSignViaPaperMode();
    }
}