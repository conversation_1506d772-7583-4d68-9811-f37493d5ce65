package com.magnamedia.scheduledjobs;
/**
 * <AUTHOR>
 * @created 05/05/2024 - 12:32 PM
 * ACC-7399
 */

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.Replacement;
import com.magnamedia.extra.JobUtils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.ReplacementRepository;
import com.magnamedia.service.MessagingService;
import com.magnamedia.service.SwitchingNationalityService;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class CheckReplacementFailedAfterUpgradingFeeJob implements MagnamediaJob {

    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    private ReplacementRepository replacementRepository;
    private SwitchingNationalityService switchingNationalityService;
    private final String REPLACEMENT_FAILED_JOB_LAST_CHECK_DATE = "check_replacement_failed_after_upgrading_fee_job_last_check_date";
    private static final Logger logger = Logger.getLogger(CheckReplacementFailedAfterUpgradingFeeJob.class.getName());

    @Override
    public void run(Map<String, ?> map) {
        contractPaymentConfirmationToDoRepository = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);
        replacementRepository = Setup.getRepository(ReplacementRepository.class);
        switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
        this.runJob();
    }

    private void runJob() {
        logger.info("started");

        AccountingEntityProperty lastCheckDateProperty = Setup.getRepository(AccountingEntityPropertyRepository.class)
                .findByKeyAndIsDeletedFalse(REPLACEMENT_FAILED_JOB_LAST_CHECK_DATE);

        Date lastRunDate = JobUtils.getJobLastRunDate(lastCheckDateProperty, new LocalDate().minusDays(1).toDate());
        LocalDateTime nextLastRunDate = new LocalDateTime();
        List<Map<String, Object>> contractsWithCountToDo = contractPaymentConfirmationToDoRepository.findContractsSwitchingNationalityLastDay(lastRunDate);
        logger.info( "Contract numbers: " + contractsWithCountToDo.size());

        contractsWithCountToDo.forEach(c -> {
            try {
                logger.info( "contract id: " + c.get("cId") + "; numbersTodo: " + c.get("numbersTodo"));
                List<Replacement> replacements = replacementRepository.findByContractAndCreationDateGreaterThanEqual(
                        (Long)c.get("cId") , lastRunDate)
                        .stream()
                        .filter(r -> {
                            Housemaid oldHousemaid = r.getOldHousemaid();

                            // if not found old housemaid get from contract revision
                            if (oldHousemaid == null) {
                                oldHousemaid = getOldHousemaid(r.getContract(), r.getCreationDate());
                                if (oldHousemaid == null) return false;
                                logger.info( "oldHousemaid id: " + oldHousemaid.getId());
                            }

                            return switchingNationalityService.getSwitchingNationalityType(
                                            oldHousemaid,
                                            r.getNewHousemaid())
                                    .equals(SwitchingNationalityService.SwitchingNationalityType.UPGRADING);
                        })
                        .collect(Collectors.toList());

                if ((Long)c.get("numbersTodo") <= replacements.size()) return;

                logger.info("replacements size: " + replacements.size());
                sendEmail(c.get("clientName").toString());
                Thread.sleep(2000);

            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        JobUtils.setJobLastRunDate(lastCheckDateProperty, REPLACEMENT_FAILED_JOB_LAST_CHECK_DATE, nextLastRunDate);
        logger.info("finished");
    }

    public void sendEmail(String clientName) {

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "switch_nationality_fees_mapped_replacement_failed_email_recipients",
                        new HashMap<String, String>(){{
                            put("client_name", clientName);
                        }},
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_UPGRADING_REPLACEMENT_FAILED_AFTER_CLIENT_PAYING_UPGRADING_FEE_EMAIL_RECIPIENTS),
                        "Upgrading replacement failed after the client paying upgrading fee");
    }

    public Housemaid getOldHousemaid(Contract contract, Date replacementDate) {

        HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
        query.filterBy("id", "=", contract.getId());
        query.filterBy("lastModificationDate", "<", replacementDate);
        query.filterBy("housemaid", "is not null", null);
        query.sortBy("lastModificationDate", false);
        query.setLimit(1);
        List<Contract> history = query.execute();
        logger.info( "Contract history size: " + history.size());

        return !history.isEmpty() ? history.get(0).getHousemaid() : null;
    }
}