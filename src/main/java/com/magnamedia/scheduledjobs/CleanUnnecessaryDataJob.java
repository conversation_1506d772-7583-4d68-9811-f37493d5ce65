package com.magnamedia.scheduledjobs;


import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.BankDirectDebitActivationRecordService;
import com.magnamedia.service.BankDirectDebitCancellationRecordService;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @created 08/07/2025 - 3:24 AM
 * ACC-9606
 */
public class CleanUnnecessaryDataJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(CleanUnnecessaryDataJob.class.getName());

    private static boolean isRunning = false;

    private final BankDirectDebitActivationRecordService bankDirectDebitActivationRecordService;
    private final BankDirectDebitCancellationRecordService bankDirectDebitCancellationRecordService;

    private final BankDirectDebitActivationFileRepository bankDirectDebitActivationFileRepository;
    private final BankDirectDebitActivationRecordRepository bankDirectDebitActivationRecordRepository;
    private final BankDirectDebitCancelationFileRepository bankDirectDebitCancelationFileRepository;
    private final BankDirectDebitCancelationRecordRepository bankDirectDebitCancelationRecordRepository;
    private final PaymentRepository paymentRepository;

    public CleanUnnecessaryDataJob() {
        bankDirectDebitActivationRecordService = Setup.getApplicationContext().getBean(BankDirectDebitActivationRecordService.class);
        bankDirectDebitCancellationRecordService = Setup.getApplicationContext().getBean(BankDirectDebitCancellationRecordService.class);
        bankDirectDebitActivationFileRepository = Setup.getRepository(BankDirectDebitActivationFileRepository.class);
        bankDirectDebitActivationRecordRepository = Setup.getRepository(BankDirectDebitActivationRecordRepository.class);
        bankDirectDebitCancelationFileRepository = Setup.getRepository(BankDirectDebitCancelationFileRepository.class);
        bankDirectDebitCancelationRecordRepository = Setup.getRepository(BankDirectDebitCancelationRecordRepository.class);
        paymentRepository = Setup.getRepository(PaymentRepository.class);
    }

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("Started job");
        if (isRunning) {
            logger.info("Another instance is running");
            return;
        }

        isRunning = true;

        try {
            run();
        } catch (Exception e) {
            e.printStackTrace();
        }

        isRunning = false;
        logger.info( "Ended job");
    }


    public void run() {
        processBankDirectDebitActivationFiles();
        processBankDirectDebitCancellationFiles();
        processPayments();
    }

    private void processBankDirectDebitActivationFiles() {
        Page<BankDirectDebitActivationFile> page;
        int pageMaxCount = 5;
        int pageCount = 0;

        do {
            pageCount++;
            page = bankDirectDebitActivationFileRepository.getAllBankDirectDebitActivationFiles(PageRequest.of(0, 100));

            logger.info(String.format("Page: %d, Size: %d", pageCount, page.getContent().size()));

            // Process each entity in the current page
            page.getContent()
                    .forEach(entity -> {
                        try {
                            processBankDirectDebitActivationFile(entity);
                        } catch (Exception e) {
                            logger.severe(String.format("Error processing entity with ID: %d, Error: %s", entity.getId() , e.getMessage()));
                            e.printStackTrace();
                        }
                    });
        } while (!page.getContent().isEmpty() && pageCount < pageMaxCount);
    }

    private void processBankDirectDebitActivationFile(BankDirectDebitActivationFile file) {
        logger.info("BankDirectDebitActivationFile id: " + file.getId());
        List<BankDirectDebitActivationRecord> activationRecords = bankDirectDebitActivationRecordRepository.findRecordsByFileId(file.getId());
        if (bankDirectDebitActivationRecordService.checkNewFile(activationRecords)) return;

        bankDirectDebitActivationRecordRepository.deleteAll(activationRecords);
        bankDirectDebitActivationFileRepository.delete(file);
    }

    private void processBankDirectDebitCancellationFiles() {
        Page<BankDirectDebitCancelationFile> page;
        int pageMaxCount = 5;
        int pageCount = 0;

        do {
            pageCount++;
            page = bankDirectDebitCancelationFileRepository.getAllBankDirectDebitCancellationFiles(PageRequest.of(0, 100));

            logger.info(String.format("Page: %d, Size: %d", pageCount, page.getContent().size()));

            // Process each entity in the current page
            page.getContent()
                    .forEach(entity -> {
                        try {
                            processBankDirectDebitCancellationFile(entity);
                        } catch (Exception e) {
                            logger.severe(String.format("Error processing entity with ID: %d, Error: %s", entity.getId() , e.getMessage()));
                            e.printStackTrace();
                        }
                    });
        } while (!page.getContent().isEmpty() || pageCount >= pageMaxCount);
    }

    private void processBankDirectDebitCancellationFile(BankDirectDebitCancelationFile file) {
        logger.info("BankDirectDebitCancellationFile id: " + file.getId());
        List<BankDirectDebitCancelationRecord> cancellationRecords = bankDirectDebitCancelationRecordRepository.findRecordsByFileId(file.getId());
        if (bankDirectDebitCancellationRecordService.checkNewFile(cancellationRecords)) return;

        bankDirectDebitCancelationRecordRepository.deleteAll(cancellationRecords);
        bankDirectDebitCancelationFileRepository.delete(file);
    }

    private void processPayments() {
        // Handle Recurring Payments
        logger.info("Start Recurring Payments");

        Page<Payment> page;
        int pageMaxCount = 5;
        int pageCount = 0;

        do {
            pageCount++;
            page = paymentRepository.findAllPaymentsRecurringAndDeleted(PageRequest.of(0, 100));

            logger.info(String.format("Page: %d, Size: %d", pageCount, page.getContent().size()));

            // Process each entity in the current page
            page.getContent()
                    .forEach(entity -> {
                        try {
                            processPayment(entity);
                        } catch (Exception e) {
                            logger.severe(String.format("Error processing entity with ID: %d, Error: %s", entity.getId() , e.getMessage()));
                            e.printStackTrace();
                        }
                    });
        } while (!page.getContent().isEmpty() || pageCount >= pageMaxCount);
        logger.info("End Recurring Payments");

        // Handle Payments Related Rejected DDs
        logger.info("Start Payments Related Rejected DDs");
        pageMaxCount = 50;
        pageCount = 0;
        do {
            pageCount++;
            page = paymentRepository.findAllPaymentsRejectDdAndDeleted(PageRequest.of(0, 200));

            logger.info(String.format("Page: %d, Size: %d", pageCount, page.getContent().size()));

            // Process each entity in the current page
            page.getContent()
                    .forEach(entity -> {
                        try {
                            processPayment(entity);
                        } catch (Exception e) {
                            logger.severe(String.format("Error processing entity with ID: %d, Error: %s", entity.getId() , e.getMessage()));
                            e.printStackTrace();
                        }
                    });
        } while (!page.getContent().isEmpty() || pageCount >= pageMaxCount);
        logger.info("End Payments Related Rejected DDs");
    }

    private void processPayment(Payment payment) {
        logger.info("Delete payment id: " + payment.getId() +
                "; date: " + new LocalDate(payment.getDateOfPayment()).toString("yyyy-MM-dd") +
                "; type: " + payment.getTypeOfPayment().getCode() +
                "; amount: " + payment.getAmountOfPayment() +
                "; recurring: " + payment.getRecurring() +
                "; directDebit id: " + (payment.getDirectDebit() != null ? payment.getDirectDebit() : "NULL"));
        payment.setDeepSilentSave(true);
        paymentRepository.delete(payment);
    }
}