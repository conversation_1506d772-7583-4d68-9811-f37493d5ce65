package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.extra.VisaStatementTransactionType;
import com.magnamedia.repository.VisaStatementTransactionRepository;
import com.magnamedia.service.MessagingService;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.VisaStatementService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import static com.magnamedia.module.AccountingModule.PARAMETER_SEND_VISA_EXPENSE_MISSING_ERP_ALERT_DEPLOYMENT_DATE;

public class SendMissingVisaStatementTransactionAlertsJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(SendMissingVisaStatementTransactionAlertsJob.class.getName());
    private VisaStatementTransactionRepository transactionRepository;
    private MessagingService messagingService;
    private VisaStatementService visaStatementService;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Job started");
        transactionRepository = Setup.getRepository(VisaStatementTransactionRepository.class);
        messagingService = Setup.getApplicationContext().getBean(MessagingService.class);
        visaStatementService = Setup.getApplicationContext().getBean(VisaStatementService.class);

        sendDailyMissingERPAlerts();

        logger.log(Level.INFO, "Job finished");
    }

    private void sendDailyMissingERPAlerts() {

        Long lastId = -1L;
        Page<Long> p;


        do {
            p = transactionRepository
                    .findByTypeAndFinishedFalse(java.sql.Date.valueOf(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_SEND_VISA_EXPENSE_MISSING_ERP_ALERT_DEPLOYMENT_DATE).trim()),
                            lastId, VisaStatementTransactionType.MissingFromERP, PageRequest.of(0, 200));

            p.forEach(vt -> {

                List<Map> statementRecords = transactionRepository
                        .findMatchedByStatement(vt, VisaStatementTransactionType.Matched);

                statementRecords.addAll(transactionRepository
                        .findMissingAndNotManullayAddedTransactionByStatement(vt, VisaStatementTransactionType.MissingFromERP));

                if (statementRecords.isEmpty()) return; // Skip if no records found

                List<Map> recordsThreeDaysUnclosed = statementRecords.stream().filter(
                                m -> m.get("creationDate") != null &&
                                        (new Date().getTime() - ((Date) m.get("creationDate")).getTime()) > 3 * 24 * 60 * 60 * 1000)
                        .collect(Collectors.toList());

                logger.info("recordsThreeDaysUnclosed size: " + recordsThreeDaysUnclosed.size());

                // Send alerts for records unclosed for more than 3 days
                if (!recordsThreeDaysUnclosed.isEmpty()) {
                    sendMissingERPAlerts(recordsThreeDaysUnclosed,
                            AccountingModule.PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS_AFTER_THREE_DAYS_UNCLOSED);
                }

                // Send alerts for all missing ERP records
                sendMissingERPAlerts(statementRecords,
                        AccountingModule.PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS);
            });

            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1);
            }
        }  while (!p.getContent().isEmpty());
    }

    private void sendMissingERPAlerts(List<Map> records, String recipientParameter) {
        records.stream()
                .filter(record -> record.get("type") != null &&
                        VisaStatementTransactionType.valueOf(record.get("type").toString())
                                .equals(VisaStatementTransactionType.MissingFromERP))
                .forEach(transaction -> {
                    messagingService.sendEmailToOfficeStaff(
                            "missing_visa_expense_alert",
                            visaStatementService.fillParams(transaction, records.stream().filter(record ->
                                    (record.get("id") != null && transaction.get("id") != null &&
                                            !Objects.equals((Long) record.get("id"), (Long) transaction.get("id"))) &&
                                                    (record.get("amount") != null &&
                                                            transaction.get("amount") != null &&
                                                            ((Double) record.get("amount")).equals((Double) transaction.get("amount"))) &&
                                                            VisaStatementTransactionType.valueOf(record.get("type").toString())
                                                                    .equals(VisaStatementTransactionType.Matched))
                                            .collect(Collectors.toList()),
                                    records.stream().filter(record ->
                                            (record.get("id") != null && transaction.get("id") != null &&
                                            !Objects.equals((Long) record.get("id"), (Long) transaction.get("id"))) &&
                                                    (record.get("amount") != null &&
                                                    transaction.get("amount") != null &&
                                                    ((Double) record.get("amount")).equals((Double) transaction.get("amount"))) &&
                                                    VisaStatementTransactionType.valueOf(record.get("type").toString())
                                                            .equals(VisaStatementTransactionType.MissingFromERP)).count(),
                                    records.get(0).get("fileUploadDate") != null ? ((Date) records.get(0).get("fileUploadDate")) : new Date()),
                            Setup.getParameter(Setup.getCurrentModule(), recipientParameter),
                            "ACTION NEEDED: A Transaction is Missing in Visa Expenses");
                });
    }

}