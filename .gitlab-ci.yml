variables:
 # This will supress any download for dependencies and plugins or upload messages which would clutter the console log.
 # `showDateTime` will show the passed time in milliseconds. You need to specify `--batch-mode` to make this work.
 MAVEN_OPTS: "-Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
 #MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
 # As of Maven 3.3.0 instead of this you may define these options in `.mvn/maven.config` so the same config is used
 # when running from the command line.
 # `installAtEnd` and `deployAtEnd`are only effective with recent version of the corresponding plugins.
 MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version -Dfile.encoding=UTF-8 -DinstallAtEnd=true -DdeployAtEnd=true -P autoaudit"
 PROJECT: 'accounting'
 DEV_FILES_PATH: '/result/backend/dev/'
 TEST_FILES_PATH: '/result/backend/tz/'
 STAGING1_FILES_PATH: '/result/backend/stg1/'
 STAGING2_FILES_PATH: '/result/backend/stg2/'
 PRODUCTION_FILES_PATH: '/result/backend/prod/'


stages:
 - DEV
 - TZ
 - STG1
 - STG2
 - PROD

DEV:
  stage: DEV
  when: manual
  script:
    - mvn $MAVEN_CLI_OPTS clean install verify
    - mkdir -p $DEV_FILES_PATH
    - rm -f $DEV_FILES_PATH/$PROJECT.war
    - cp target/*.war $DEV_FILES_PATH/$PROJECT.war

  tags:
    - java_1_8_maven_3_8_1_builder

TZ:
  stage: TZ
  when: manual
  only:
    - /^testzone|^master$/
  script:
    - mvn $MAVEN_CLI_OPTS clean install verify
    - mkdir -p $TEST_FILES_PATH
    - rm -f $TEST_FILES_PATH/$PROJECT.war
    - cp target/*.war $TEST_FILES_PATH/$PROJECT.war

  tags:
    - java_1_8_maven_3_8_1_builder

STG1:
  stage: STG1
  when: manual
  only:
       - /^version_STAGING1_|^master$/
  script:
    - mvn $MAVEN_CLI_OPTS clean install verify
    - mkdir -p $STAGING1_FILES_PATH
    - rm -f $STAGING1_FILES_PATH/$PROJECT.war
    - cp target/*.war $STAGING1_FILES_PATH/$PROJECT.war

  tags:
    - java_1_8_maven_3_8_1_builder

STG2:
  stage: STG2
  when: manual
  only:
       - /^version_STAGING2_|^master$/
  script:
    - mvn $MAVEN_CLI_OPTS clean install verify
    - mkdir -p $STAGING2_FILES_PATH
    - rm -f $STAGING2_FILES_PATH/$PROJECT.war
    - cp target/*.war $STAGING2_FILES_PATH/$PROJECT.war

  tags:
    - java_1_8_maven_3_8_1_builder

PROD:
  stage: PROD
  when: manual
  only:
       - /^master$/
  script:
    - mvn $MAVEN_CLI_OPTS clean install verify
    - mkdir -p $PRODUCTION_FILES_PATH
    - rm -f $PRODUCTION_FILES_PATH/$PROJECT.war
    - cp target/*.war $PRODUCTION_FILES_PATH/$PROJECT.war

  tags:
    - java_1_8_maven_3_8_1_builder


